"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/landing/HeroSection.tsx":
/*!************************************************!*\
  !*** ./src/components/landing/HeroSection.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HeroSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_BoltIcon_PlayIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,BoltIcon,PlayIcon,ShieldCheckIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_BoltIcon_PlayIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,BoltIcon,PlayIcon,ShieldCheckIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_BoltIcon_PlayIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,BoltIcon,PlayIcon,ShieldCheckIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_BoltIcon_PlayIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,BoltIcon,PlayIcon,ShieldCheckIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_BoltIcon_PlayIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,BoltIcon,PlayIcon,ShieldCheckIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlayIcon.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction HeroSection() {\n    _s();\n    const [isVideoPlaying, setIsVideoPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [typewriterText, setTypewriterText] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)('');\n    const [currentWordIndex, setCurrentWordIndex] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(0);\n    const words = [\n        'Intelligent',\n        'Powerful',\n        'Seamless',\n        'Revolutionary'\n    ];\n    const fullText = 'RouKey: The Ultimate AI Gateway';\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)({\n        \"HeroSection.useEffect\": ()=>{\n            // Typewriter effect for the main title\n            let i = 0;\n            const typeInterval = setInterval({\n                \"HeroSection.useEffect.typeInterval\": ()=>{\n                    if (i < fullText.length) {\n                        setTypewriterText(fullText.slice(0, i + 1));\n                        i++;\n                    } else {\n                        clearInterval(typeInterval);\n                    }\n                }\n            }[\"HeroSection.useEffect.typeInterval\"], 80);\n            // Rotating words effect\n            const wordInterval = setInterval({\n                \"HeroSection.useEffect.wordInterval\": ()=>{\n                    setCurrentWordIndex({\n                        \"HeroSection.useEffect.wordInterval\": (prev)=>(prev + 1) % words.length\n                    }[\"HeroSection.useEffect.wordInterval\"]);\n                }\n            }[\"HeroSection.useEffect.wordInterval\"], 2000);\n            return ({\n                \"HeroSection.useEffect\": ()=>{\n                    clearInterval(typeInterval);\n                    clearInterval(wordInterval);\n                }\n            })[\"HeroSection.useEffect\"];\n        }\n    }[\"HeroSection.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"relative min-h-screen bg-black overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"absolute inset-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-black\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            backgroundImage: \"\\n              linear-gradient(rgba(255, 107, 53, 0.1) 1px, transparent 1px),\\n              linear-gradient(90deg, rgba(255, 107, 53, 0.1) 1px, transparent 1px)\\n            \",\n                            backgroundSize: '50px 50px',\n                            animation: 'grid-move 20s linear infinite'\n                        },\n                        className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"absolute inset-0 opacity-20\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"absolute inset-0 bg-gradient-to-t from-black via-transparent to-black opacity-60\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-orange-500/20 to-red-500/20 rounded-full blur-3xl animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            animationDelay: '2s'\n                        },\n                        className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"absolute bottom-1/4 right-1/4 w-96 h-96 bg-gradient-to-r from-orange-600/10 to-yellow-500/10 rounded-full blur-3xl animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-32 pb-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center min-h-[80vh]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: -50\n                            },\n                            animate: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                duration: 1\n                            },\n                            className: \"text-left\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"mb-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"text-5xl md:text-6xl font-bold text-white mb-4 leading-tight max-w-6xl\",\n                                            children: [\n                                                typewriterText,\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"animate-pulse text-[#ff6b35]\",\n                                                    children: \"|\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                    lineNumber: 78,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"text-3xl md:text-4xl lg:text-5xl font-bold mb-4 max-w-6xl\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"text-gray-300\",\n                                                    children: \"Make AI \"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                    lineNumber: 82,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"text-transparent bg-clip-text bg-gradient-to-r from-[#ff6b35] to-[#f7931e] transition-all duration-500\",\n                                                    children: words[currentWordIndex]\n                                                }, currentWordIndex, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                    lineNumber: 83,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"text-xl md:text-2xl text-gray-300 mb-12 leading-relaxed max-w-3xl\",\n                                    children: [\n                                        \"Route requests to the perfect AI model from\",\n                                        ' ',\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"text-[#ff6b35] font-semibold\",\n                                            children: \"300+ models\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 15\n                                        }, this),\n                                        \". Zero configuration, maximum performance.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"grid grid-cols-1 md:grid-cols-3 gap-6 mb-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"p-2 bg-[#ff6b35]/20 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_BoltIcon_PlayIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"h-6 w-6 text-[#ff6b35]\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                        lineNumber: 102,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"text-gray-300 font-medium\",\n                                                    children: \"Lightning Fast\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                    lineNumber: 104,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"p-2 bg-[#ff6b35]/20 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_BoltIcon_PlayIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-6 w-6 text-[#ff6b35]\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                        lineNumber: 108,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"text-gray-300 font-medium\",\n                                                    children: \"Enterprise Ready\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                    lineNumber: 110,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"p-2 bg-[#ff6b35]/20 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_BoltIcon_PlayIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-6 w-6 text-[#ff6b35]\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                        lineNumber: 114,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"text-gray-300 font-medium\",\n                                                    children: \"AI-Powered\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                    lineNumber: 116,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: 0.5\n                                    },\n                                    className: \"flex flex-col sm:flex-row gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/auth/signup\",\n                                            className: \"group inline-flex items-center px-8 py-4 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white font-bold rounded-xl hover:shadow-2xl hover:shadow-orange-500/25 hover:scale-105 transition-all duration-300 text-lg relative overflow-hidden\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"absolute inset-0 bg-gradient-to-r from-[#f7931e] to-[#ff6b35] opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                    lineNumber: 131,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"relative z-10\",\n                                                    children: \"Start Building Now\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                    lineNumber: 132,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_BoltIcon_PlayIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"ml-3 h-5 w-5 relative z-10 group-hover:translate-x-1 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setIsVideoPlaying(true),\n                                            className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"inline-flex items-center px-8 py-4 border-2 border-gray-600 text-gray-300 font-bold rounded-xl hover:border-[#ff6b35] hover:text-white hover:bg-[#ff6b35]/10 transition-all duration-300 text-lg group\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_BoltIcon_PlayIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"mr-3 h-5 w-5 group-hover:text-[#ff6b35] transition-colors duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                    lineNumber: 140,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Watch Demo\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        delay: 0.7\n                                    },\n                                    className: \"flex flex-col sm:flex-row gap-8 mt-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"text-left\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"text-3xl font-bold text-white\",\n                                                    children: \"300+\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"text-gray-400\",\n                                                    children: \"AI Models\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                    lineNumber: 154,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"text-left\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"text-3xl font-bold text-white\",\n                                                    children: \"99.9%\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"text-gray-400\",\n                                                    children: \"Uptime\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                    lineNumber: 158,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"text-left\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"text-3xl font-bold text-white\",\n                                                    children: \"<500ms\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                    lineNumber: 161,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"text-gray-400\",\n                                                    children: \"Response Time\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                    lineNumber: 162,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: 50\n                            },\n                            animate: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                duration: 1,\n                                delay: 0.3\n                            },\n                            className: \"relative lg:-mt-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"relative bg-gray-900/80 rounded-2xl shadow-2xl border border-gray-700 overflow-hidden backdrop-blur-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"absolute inset-0 opacity-10\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    backgroundImage: \"\\n                    linear-gradient(rgba(255, 107, 53, 0.2) 1px, transparent 1px),\\n                    linear-gradient(90deg, rgba(255, 107, 53, 0.2) 1px, transparent 1px)\\n                  \",\n                                                    backgroundSize: '30px 30px'\n                                                },\n                                                className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"w-full h-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"relative z-10 p-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"text-center mb-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"inline-flex items-center px-4 py-2 bg-gray-800 rounded-lg border border-gray-600 mb-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"w-2 h-2 bg-[#ff6b35] rounded-full animate-pulse mr-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                    lineNumber: 191,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"text-white text-sm font-medium\",\n                                                                    children: \"Intelligent Routing Active\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                    lineNumber: 192,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                            lineNumber: 190,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"text-gray-400 text-sm\",\n                                                            children: 'Request: \"Best AI for coding\"'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                            lineNumber: 194,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                    lineNumber: 189,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"flex justify-center mb-8\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"w-24 h-24 bg-white rounded-xl flex items-center justify-center shadow-2xl p-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                    src: \"/roukey_logo.png\",\n                                                                    alt: \"RouKey\",\n                                                                    width: 80,\n                                                                    height: 80,\n                                                                    className: \"object-contain\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                    lineNumber: 201,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                lineNumber: 200,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"absolute -top-2 -right-2 w-6 h-6 bg-green-500 rounded-full animate-pulse\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                lineNumber: 209,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"text-center mt-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"text-white font-bold\",\n                                                                        children: \"RouKey\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                        lineNumber: 211,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"text-gray-400 text-xs\",\n                                                                        children: \"Intelligent Router\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                        lineNumber: 212,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                lineNumber: 210,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                        lineNumber: 199,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"grid grid-cols-2 md:grid-cols-3 gap-4 mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"relative group opacity-70 hover:opacity-100 transition-opacity\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"bg-gray-800 border border-gray-600 rounded-xl p-4 shadow-lg transform transition-all duration-300 hover:scale-105\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"flex items-center justify-between mb-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"w-12 h-12 bg-white rounded-lg flex items-center justify-center p-1\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                                    src: \"/openai_logo.jpg\",\n                                                                                    alt: \"OpenAI\",\n                                                                                    width: 40,\n                                                                                    height: 40,\n                                                                                    className: \"object-contain rounded\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                                    lineNumber: 224,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                                lineNumber: 223,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"w-6 h-6 bg-green-500/50 rounded-full animate-pulse\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                                lineNumber: 232,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                        lineNumber: 222,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"text-white font-medium mb-1\",\n                                                                        children: \"OpenAI\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                        lineNumber: 234,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"text-gray-400 text-xs font-medium\",\n                                                                        children: \"AVAILABLE\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                        lineNumber: 235,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"text-gray-500 text-xs\",\n                                                                        children: \"GPT-4o\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                        lineNumber: 236,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                lineNumber: 221,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                            lineNumber: 220,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"relative group\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"bg-gray-800 border-2 border-purple-400 rounded-xl p-4 shadow-lg shadow-purple-400/25 transform transition-all duration-300 hover:scale-105\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"flex items-center justify-between mb-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"w-12 h-12 bg-white rounded-lg flex items-center justify-center p-1\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                                    src: \"/claude_logo.png\",\n                                                                                    alt: \"Anthropic\",\n                                                                                    width: 40,\n                                                                                    height: 40,\n                                                                                    className: \"object-contain\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                                    lineNumber: 245,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                                lineNumber: 244,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"text-white text-xs\",\n                                                                                    children: \"✓\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                                    lineNumber: 254,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                                lineNumber: 253,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                        lineNumber: 243,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"text-white font-medium mb-1\",\n                                                                        children: \"Anthropic\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                        lineNumber: 257,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"text-purple-400 text-xs font-medium\",\n                                                                        children: \"SELECTED\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                        lineNumber: 258,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"text-gray-400 text-xs\",\n                                                                        children: \"Claude 4 Opus • Best for coding\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                        lineNumber: 259,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                lineNumber: 242,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                            lineNumber: 241,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"relative group opacity-70 hover:opacity-100 transition-opacity\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"bg-gray-800 border border-gray-600 rounded-xl p-4 shadow-lg transform transition-all duration-300 hover:scale-105\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"flex items-center justify-between mb-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"w-12 h-12 bg-white rounded-lg flex items-center justify-center p-1\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                                    src: \"/gemini_logo.png\",\n                                                                                    alt: \"Google Gemini\",\n                                                                                    width: 40,\n                                                                                    height: 40,\n                                                                                    className: \"object-contain\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                                    lineNumber: 268,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                                lineNumber: 267,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"w-6 h-6 bg-blue-500/50 rounded-full animate-pulse\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                                lineNumber: 276,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                        lineNumber: 266,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"text-white font-medium mb-1\",\n                                                                        children: \"Google\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                        lineNumber: 278,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"text-gray-400 text-xs font-medium\",\n                                                                        children: \"AVAILABLE\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                        lineNumber: 279,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"text-gray-500 text-xs\",\n                                                                        children: \"Gemini Pro\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                        lineNumber: 280,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                lineNumber: 265,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                            lineNumber: 264,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"relative group opacity-70 hover:opacity-100 transition-opacity\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"bg-gray-800 border border-gray-600 rounded-xl p-4 shadow-lg transform transition-all duration-300 hover:scale-105\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"flex items-center justify-between mb-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"w-12 h-12 bg-white rounded-lg flex items-center justify-center p-1\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                                    src: \"/deepseek_logo.png\",\n                                                                                    alt: \"Deepseek\",\n                                                                                    width: 40,\n                                                                                    height: 40,\n                                                                                    className: \"object-contain\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                                    lineNumber: 289,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                                lineNumber: 288,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"w-6 h-6 bg-cyan-500/50 rounded-full animate-pulse\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                                lineNumber: 297,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                        lineNumber: 287,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"text-white font-medium mb-1\",\n                                                                        children: \"Deepseek\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                        lineNumber: 299,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"text-gray-400 text-xs font-medium\",\n                                                                        children: \"AVAILABLE\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                        lineNumber: 300,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"text-gray-500 text-xs\",\n                                                                        children: \"DeepSeek-R1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                        lineNumber: 301,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                lineNumber: 286,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                            lineNumber: 285,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"relative group opacity-70 hover:opacity-100 transition-opacity\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"bg-gray-800 border border-gray-600 rounded-xl p-4 shadow-lg transform transition-all duration-300 hover:scale-105\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"flex items-center justify-between mb-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"w-12 h-12 bg-white rounded-lg flex items-center justify-center p-1\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                                    src: \"/mistral_logo.png\",\n                                                                                    alt: \"Mistral\",\n                                                                                    width: 40,\n                                                                                    height: 40,\n                                                                                    className: \"object-contain\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                                    lineNumber: 310,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                                lineNumber: 309,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"w-6 h-6 bg-red-500/50 rounded-full animate-pulse\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                                lineNumber: 318,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                        lineNumber: 308,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"text-white font-medium mb-1\",\n                                                                        children: \"Mistral\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                        lineNumber: 320,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"text-gray-400 text-xs font-medium\",\n                                                                        children: \"AVAILABLE\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                        lineNumber: 321,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"text-gray-500 text-xs\",\n                                                                        children: \"Mistral Large\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                        lineNumber: 322,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                lineNumber: 307,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                            lineNumber: 306,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"relative group opacity-50 hover:opacity-70 transition-opacity\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"bg-gray-800 border border-gray-600 border-dashed rounded-xl p-4 shadow-lg transform transition-all duration-300 hover:scale-105\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"flex items-center justify-between mb-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"w-12 h-12 bg-gray-600/20 rounded-lg flex items-center justify-center\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"text-gray-400 font-bold text-lg\",\n                                                                                    children: \"+\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                                    lineNumber: 331,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                                lineNumber: 330,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"w-6 h-6 bg-gray-500/30 rounded-full\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                                lineNumber: 333,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                        lineNumber: 329,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"text-gray-400 font-medium mb-1\",\n                                                                        children: \"300+ Models\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                        lineNumber: 335,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"text-gray-500 text-xs font-medium\",\n                                                                        children: \"AVAILABLE\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                        lineNumber: 336,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"text-gray-600 text-xs\",\n                                                                        children: \"Perplexity, Groq & more\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                        lineNumber: 337,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                lineNumber: 328,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                            lineNumber: 327,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"flex items-center justify-between bg-gray-800/50 rounded-lg p-4 border border-gray-700\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"w-3 h-3 bg-green-400 rounded-full animate-pulse mr-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                    lineNumber: 345,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"text-white text-sm font-medium\",\n                                                                    children: \"Best model selected automatically\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                    lineNumber: 346,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                            lineNumber: 344,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"text-right\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"text-[#ff6b35] text-lg font-bold\",\n                                                                    children: \"$1,247\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                    lineNumber: 349,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"text-gray-400 text-xs\",\n                                                                    children: \"Cost Saved\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                    lineNumber: 350,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                            lineNumber: 348,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                    lineNumber: 343,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                    animate: {\n                                        y: [\n                                            0,\n                                            -10,\n                                            0\n                                        ]\n                                    },\n                                    transition: {\n                                        duration: 3,\n                                        repeat: Infinity\n                                    },\n                                    className: \"absolute -top-4 -right-4 bg-gray-900 border border-gray-700 rounded-lg shadow-2xl p-4 backdrop-blur-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"text-xs text-gray-400 mb-1\",\n                                            children: \"Cost Saved\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"text-xl font-bold text-[#ff6b35]\",\n                                            children: \"$1,247\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                    lineNumber: 357,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                    animate: {\n                                        y: [\n                                            0,\n                                            10,\n                                            0\n                                        ]\n                                    },\n                                    transition: {\n                                        duration: 4,\n                                        repeat: Infinity,\n                                        delay: 1\n                                    },\n                                    className: \"absolute -bottom-4 -left-4 bg-gray-900 border border-gray-700 rounded-lg shadow-2xl p-4 backdrop-blur-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"text-xs text-gray-400 mb-1\",\n                                            children: \"Requests Today\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-b6f9d45c2cfe3c34\" + \" \" + \"text-xl font-bold text-[#ff6b35]\",\n                                            children: \"2,847\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                            lineNumber: 372,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                    lineNumber: 366,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"b6f9d45c2cfe3c34\",\n                children: \"@-webkit-keyframes grid-move{0%{-webkit-transform:translate(0,0);transform:translate(0,0)}100%{-webkit-transform:translate(50px,50px);transform:translate(50px,50px)}}@-moz-keyframes grid-move{0%{-moz-transform:translate(0,0);transform:translate(0,0)}100%{-moz-transform:translate(50px,50px);transform:translate(50px,50px)}}@-o-keyframes grid-move{0%{-o-transform:translate(0,0);transform:translate(0,0)}100%{-o-transform:translate(50px,50px);transform:translate(50px,50px)}}@keyframes grid-move{0%{-webkit-transform:translate(0,0);-moz-transform:translate(0,0);-o-transform:translate(0,0);transform:translate(0,0)}100%{-webkit-transform:translate(50px,50px);-moz-transform:translate(50px,50px);-o-transform:translate(50px,50px);transform:translate(50px,50px)}}\"\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, this);\n}\n_s(HeroSection, \"fc7P9Roz/cASDJyWAddCx+gdYU8=\");\n_c = HeroSection;\nvar _c;\n$RefreshReg$(_c, \"HeroSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/HeroSection.tsx\n"));

/***/ })

});