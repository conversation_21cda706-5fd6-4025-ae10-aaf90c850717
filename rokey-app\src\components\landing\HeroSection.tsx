'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import Image from 'next/image';
import { ArrowRightIcon, PlayIcon, SparklesIcon, BoltIcon, ShieldCheckIcon } from '@heroicons/react/24/outline';
import { useState, useEffect } from 'react';

export default function HeroSection() {
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);
  const [typewriterText, setTypewriterText] = useState('');
  const [currentWordIndex, setCurrentWordIndex] = useState(0);

  const words = ['Intelligent', 'Powerful', 'Seamless', 'Revolutionary'];
  const fullText = 'RouKey: The Ultimate AI Gateway';

  useEffect(() => {
    // Typewriter effect for the main title
    let i = 0;
    const typeInterval = setInterval(() => {
      if (i < fullText.length) {
        setTypewriterText(fullText.slice(0, i + 1));
        i++;
      } else {
        clearInterval(typeInterval);
      }
    }, 80);

    // Rotating words effect
    const wordInterval = setInterval(() => {
      setCurrentWordIndex((prev) => (prev + 1) % words.length);
    }, 2000);

    return () => {
      clearInterval(typeInterval);
      clearInterval(wordInterval);
    };
  }, []);

  return (
    <section className="relative min-h-screen bg-black overflow-hidden">
      {/* Animated Grid Background */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-black"></div>
        <div
          className="absolute inset-0 opacity-20"
          style={{
            backgroundImage: `
              linear-gradient(rgba(255, 107, 53, 0.1) 1px, transparent 1px),
              linear-gradient(90deg, rgba(255, 107, 53, 0.1) 1px, transparent 1px)
            `,
            backgroundSize: '50px 50px',
            animation: 'grid-move 20s linear infinite'
          }}
        ></div>

        {/* Smoky overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-black via-transparent to-black opacity-60"></div>

        {/* Floating orbs */}
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-orange-500/20 to-red-500/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-gradient-to-r from-orange-600/10 to-yellow-500/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }}></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-32 pb-20">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center min-h-[80vh]">
          {/* Left Column - Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 1 }}
            className="text-left"
          >
            {/* Typewriter Title */}
            <div className="mb-8">
              <h1 className="text-5xl md:text-6xl font-bold text-white mb-4 leading-tight max-w-6xl">
                {typewriterText}
                <span className="animate-pulse text-[#ff6b35]">|</span>
              </h1>

              <div className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4 max-w-6xl">
                <span className="text-gray-300">Make AI </span>
                <span
                  className="text-transparent bg-clip-text bg-gradient-to-r from-[#ff6b35] to-[#f7931e] transition-all duration-500"
                  key={currentWordIndex}
                >
                  {words[currentWordIndex]}
                </span>
              </div>
            </div>

            <p className="text-xl md:text-2xl text-gray-300 mb-12 leading-relaxed max-w-3xl">
              <span className="text-[#ff6b35] font-semibold">Unlimited requests</span> to{' '}
              <span className="text-[#ff6b35] font-semibold">300+ AI models</span>.
              Zero configuration, maximum performance.
            </p>

            {/* Feature highlights */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-[#ff6b35]/20 rounded-lg">
                  <BoltIcon className="h-6 w-6 text-[#ff6b35]" />
                </div>
                <span className="text-gray-300 font-medium">Lightning Fast</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-[#ff6b35]/20 rounded-lg">
                  <ShieldCheckIcon className="h-6 w-6 text-[#ff6b35]" />
                </div>
                <span className="text-gray-300 font-medium">Enterprise Ready</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-[#ff6b35]/20 rounded-lg">
                  <SparklesIcon className="h-6 w-6 text-[#ff6b35]" />
                </div>
                <span className="text-gray-300 font-medium">AI-Powered</span>
              </div>
            </div>

            {/* CTA Buttons */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.5 }}
              className="flex flex-col sm:flex-row gap-6"
            >
              <Link
                href="/auth/signup"
                className="group inline-flex items-center px-8 py-4 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white font-bold rounded-xl hover:shadow-2xl hover:shadow-orange-500/25 hover:scale-105 transition-all duration-300 text-lg relative overflow-hidden"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-[#f7931e] to-[#ff6b35] opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <span className="relative z-10">Start Building Now</span>
                <ArrowRightIcon className="ml-3 h-5 w-5 relative z-10 group-hover:translate-x-1 transition-transform duration-300" />
              </Link>

              <button
                onClick={() => setIsVideoPlaying(true)}
                className="inline-flex items-center px-8 py-4 border-2 border-gray-600 text-gray-300 font-bold rounded-xl hover:border-[#ff6b35] hover:text-white hover:bg-[#ff6b35]/10 transition-all duration-300 text-lg group"
              >
                <PlayIcon className="mr-3 h-5 w-5 group-hover:text-[#ff6b35] transition-colors duration-300" />
                Watch Demo
              </button>
            </motion.div>

            {/* Stats */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.7 }}
              className="flex flex-col sm:flex-row gap-8 mt-12"
            >
              <div className="text-left">
                <div className="text-3xl font-bold text-white">300+</div>
                <div className="text-gray-400">AI Models</div>
              </div>
              <div className="text-left">
                <div className="text-3xl font-bold text-white">99.9%</div>
                <div className="text-gray-400">Uptime</div>
              </div>
              <div className="text-left">
                <div className="text-3xl font-bold text-white">&lt;500ms</div>
                <div className="text-gray-400">Response Time</div>
              </div>
            </motion.div>
          </motion.div>

          {/* Right Column - Premium Visual */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 1, delay: 0.3 }}
            className="relative lg:-mt-16"
          >
            {/* AI Routing Dashboard */}
            <div className="relative bg-gray-900/80 rounded-2xl shadow-2xl border border-gray-700 overflow-hidden backdrop-blur-sm">
              {/* Grid Background */}
              <div className="absolute inset-0 opacity-10">
                <div className="w-full h-full" style={{
                  backgroundImage: `
                    linear-gradient(rgba(255, 107, 53, 0.2) 1px, transparent 1px),
                    linear-gradient(90deg, rgba(255, 107, 53, 0.2) 1px, transparent 1px)
                  `,
                  backgroundSize: '30px 30px'
                }}></div>
              </div>

              <div className="relative z-10 p-8">
                {/* Header */}
                <div className="text-center mb-8">
                  <div className="inline-flex items-center px-4 py-2 bg-gray-800 rounded-lg border border-gray-600 mb-4">
                    <div className="w-2 h-2 bg-[#ff6b35] rounded-full animate-pulse mr-3"></div>
                    <span className="text-white text-sm font-medium">Intelligent Routing Active</span>
                  </div>
                  <div className="text-gray-400 text-sm">Request: "Best AI for coding"</div>
                </div>

                {/* Central RouKey Hub */}
                <div className="flex justify-center mb-8">
                  <div className="relative">
                    <div className="w-32 h-32 bg-white rounded-xl flex items-center justify-center shadow-2xl p-2">
                      <Image
                        src="/roukey_logo.png"
                        alt="RouKey"
                        width={112}
                        height={112}
                        className="w-full h-full object-contain"
                        priority
                      />
                    </div>
                    <div className="absolute -top-2 -right-2 w-6 h-6 bg-green-500 rounded-full animate-pulse"></div>
                    <div className="text-center mt-3">
                      <div className="text-white font-bold">RouKey</div>
                      <div className="text-gray-400 text-xs">Intelligent Router</div>
                    </div>
                  </div>
                </div>

                {/* Provider Grid */}
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-6">
                  {/* OpenAI */}
                  <div className="relative group opacity-70 hover:opacity-100 transition-opacity">
                    <div className="bg-gray-800 border border-gray-600 rounded-xl p-4 shadow-lg transform transition-all duration-300 hover:scale-105">
                      <div className="flex items-center justify-between mb-3">
                        <div className="w-16 h-16 bg-white rounded-lg flex items-center justify-center p-0">
                          <Image
                            src="/openai_logo.jpg"
                            alt="OpenAI"
                            width={64}
                            height={64}
                            className="w-full h-full object-cover rounded-lg"
                          />
                        </div>
                        <div className="w-6 h-6 bg-green-500/50 rounded-full animate-pulse"></div>
                      </div>
                      <div className="text-white font-medium mb-1">OpenAI</div>
                      <div className="text-gray-400 text-xs font-medium">AVAILABLE</div>
                      <div className="text-gray-500 text-xs">GPT-4o</div>
                    </div>
                  </div>

                  {/* Anthropic - Selected */}
                  <div className="relative group">
                    <div className="bg-gray-800 border-2 border-purple-400 rounded-xl p-4 shadow-lg shadow-purple-400/25 transform transition-all duration-300 hover:scale-105">
                      <div className="flex items-center justify-between mb-3">
                        <div className="w-16 h-16 bg-white rounded-lg flex items-center justify-center p-0">
                          <Image
                            src="/claude_logo.png"
                            alt="Anthropic"
                            width={64}
                            height={64}
                            className="w-full h-full object-cover rounded-lg"
                          />
                        </div>
                        <div className="w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center">
                          <span className="text-white text-xs">✓</span>
                        </div>
                      </div>
                      <div className="text-white font-medium mb-1">Anthropic</div>
                      <div className="text-purple-400 text-xs font-medium">SELECTED</div>
                      <div className="text-gray-400 text-xs">Claude 4 Opus • Best for coding</div>
                    </div>
                  </div>

                  {/* Google */}
                  <div className="relative group opacity-70 hover:opacity-100 transition-opacity">
                    <div className="bg-gray-800 border border-gray-600 rounded-xl p-4 shadow-lg transform transition-all duration-300 hover:scale-105">
                      <div className="flex items-center justify-between mb-3">
                        <div className="w-16 h-16 bg-white rounded-lg flex items-center justify-center p-0">
                          <Image
                            src="/gemini_logo.png"
                            alt="Google Gemini"
                            width={64}
                            height={64}
                            className="w-full h-full object-cover rounded-lg"
                          />
                        </div>
                        <div className="w-6 h-6 bg-blue-500/50 rounded-full animate-pulse"></div>
                      </div>
                      <div className="text-white font-medium mb-1">Google</div>
                      <div className="text-gray-400 text-xs font-medium">AVAILABLE</div>
                      <div className="text-gray-500 text-xs">Gemini Pro</div>
                    </div>
                  </div>

                  {/* Deepseek */}
                  <div className="relative group opacity-70 hover:opacity-100 transition-opacity">
                    <div className="bg-gray-800 border border-gray-600 rounded-xl p-4 shadow-lg transform transition-all duration-300 hover:scale-105">
                      <div className="flex items-center justify-between mb-3">
                        <div className="w-16 h-16 bg-white rounded-lg flex items-center justify-center p-0">
                          <Image
                            src="/deepseek_logo.png"
                            alt="Deepseek"
                            width={64}
                            height={64}
                            className="w-full h-full object-cover rounded-lg"
                          />
                        </div>
                        <div className="w-6 h-6 bg-cyan-500/50 rounded-full animate-pulse"></div>
                      </div>
                      <div className="text-white font-medium mb-1">Deepseek</div>
                      <div className="text-gray-400 text-xs font-medium">AVAILABLE</div>
                      <div className="text-gray-500 text-xs">DeepSeek-R1</div>
                    </div>
                  </div>

                  {/* Mistral */}
                  <div className="relative group opacity-70 hover:opacity-100 transition-opacity">
                    <div className="bg-gray-800 border border-gray-600 rounded-xl p-4 shadow-lg transform transition-all duration-300 hover:scale-105">
                      <div className="flex items-center justify-between mb-3">
                        <div className="w-12 h-12 bg-white rounded-lg flex items-center justify-center p-0.5">
                          <Image
                            src="/mistral_logo.png"
                            alt="Mistral"
                            width={44}
                            height={44}
                            className="object-cover"
                          />
                        </div>
                        <div className="w-6 h-6 bg-red-500/50 rounded-full animate-pulse"></div>
                      </div>
                      <div className="text-white font-medium mb-1">Mistral</div>
                      <div className="text-gray-400 text-xs font-medium">AVAILABLE</div>
                      <div className="text-gray-500 text-xs">Mistral Large</div>
                    </div>
                  </div>

                  {/* More Providers */}
                  <div className="relative group opacity-50 hover:opacity-70 transition-opacity">
                    <div className="bg-gray-800 border border-gray-600 border-dashed rounded-xl p-4 shadow-lg transform transition-all duration-300 hover:scale-105">
                      <div className="flex items-center justify-between mb-3">
                        <div className="w-12 h-12 bg-gray-600/20 rounded-lg flex items-center justify-center">
                          <span className="text-gray-400 font-bold text-lg">+</span>
                        </div>
                        <div className="w-6 h-6 bg-gray-500/30 rounded-full"></div>
                      </div>
                      <div className="text-gray-400 font-medium mb-1">300+ Models</div>
                      <div className="text-gray-500 text-xs font-medium">AVAILABLE</div>
                      <div className="text-gray-600 text-xs">Perplexity, Groq & more</div>
                    </div>
                  </div>
                </div>

                {/* Status Bar */}
                <div className="flex items-center justify-between bg-gray-800/50 rounded-lg p-4 border border-gray-700">
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse mr-3"></div>
                    <span className="text-white text-sm font-medium">Best model selected automatically</span>
                  </div>
                  <div className="text-right">
                    <div className="text-[#ff6b35] text-lg font-bold">$1,247</div>
                    <div className="text-gray-400 text-xs">Cost Saved</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Premium Floating elements */}
            <motion.div
              animate={{ y: [0, -10, 0] }}
              transition={{ duration: 3, repeat: Infinity }}
              className="absolute -top-4 -right-4 bg-gray-900 border border-gray-700 rounded-lg shadow-2xl p-4 backdrop-blur-sm"
            >
              <div className="text-xs text-gray-400 mb-1">Cost Saved</div>
              <div className="text-xl font-bold text-[#ff6b35]">$1,247</div>
            </motion.div>

            <motion.div
              animate={{ y: [0, 10, 0] }}
              transition={{ duration: 4, repeat: Infinity, delay: 1 }}
              className="absolute -bottom-4 -left-4 bg-gray-900 border border-gray-700 rounded-lg shadow-2xl p-4 backdrop-blur-sm"
            >
              <div className="text-xs text-gray-400 mb-1">Requests Today</div>
              <div className="text-xl font-bold text-[#ff6b35]">2,847</div>
            </motion.div>


          </motion.div>
        </div>
      </div>

      {/* Add CSS for grid animation */}
      <style jsx>{`
        @keyframes grid-move {
          0% { transform: translate(0, 0); }
          100% { transform: translate(50px, 50px); }
        }
      `}</style>
    </section>
  );
}
