"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LandingPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_serviceWorker__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/serviceWorker */ \"(app-pages-browser)/./src/utils/serviceWorker.ts\");\n/* harmony import */ var _components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/landing/LandingNavbar */ \"(app-pages-browser)/./src/components/landing/LandingNavbar.tsx\");\n/* harmony import */ var _components_landing_HeroSection__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/landing/HeroSection */ \"(app-pages-browser)/./src/components/landing/HeroSection.tsx\");\n/* harmony import */ var _components_landing_TrustBadges__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/landing/TrustBadges */ \"(app-pages-browser)/./src/components/landing/TrustBadges.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n// Lazy load non-critical sections for better initial load performance\nconst FeaturesSection = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.lazy)(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_landing_FeaturesSection_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/landing/FeaturesSection */ \"(app-pages-browser)/./src/components/landing/FeaturesSection.tsx\")));\n_c = FeaturesSection;\nconst RoutingVisualization = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.lazy)(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_landing_RoutingVisualization_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/landing/RoutingVisualization */ \"(app-pages-browser)/./src/components/landing/RoutingVisualization.tsx\")));\n_c1 = RoutingVisualization;\nconst TestimonialsSection = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.lazy)(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_landing_TestimonialsSection_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/landing/TestimonialsSection */ \"(app-pages-browser)/./src/components/landing/TestimonialsSection.tsx\")));\n_c2 = TestimonialsSection;\nconst CTASection = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.lazy)(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_landing_CTASection_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/landing/CTASection */ \"(app-pages-browser)/./src/components/landing/CTASection.tsx\")));\n_c3 = CTASection;\nconst Footer = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.lazy)(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_landing_Footer_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/landing/Footer */ \"(app-pages-browser)/./src/components/landing/Footer.tsx\")));\n_c4 = Footer;\n// Loading skeleton for lazy components\nconst SectionSkeleton = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"py-20 animate-pulse\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-8 bg-gray-200 rounded w-1/3 mx-auto mb-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-4 bg-gray-200 rounded w-2/3 mx-auto mb-8\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                    children: [\n                        1,\n                        2,\n                        3\n                    ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-48 bg-gray-200 rounded-lg\"\n                        }, i, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 11\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 19,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 18,\n        columnNumber: 3\n    }, undefined);\n_c5 = SectionSkeleton;\nfunction LandingPage() {\n    _s();\n    const [isLoaded, setIsLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [sectionsLoaded, setSectionsLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LandingPage.useEffect\": ()=>{\n            // Register service worker for caching\n            (0,_utils_serviceWorker__WEBPACK_IMPORTED_MODULE_2__.registerServiceWorker)({\n                onSuccess: {\n                    \"LandingPage.useEffect\": ()=>console.log('✅ Service Worker registered for caching')\n                }[\"LandingPage.useEffect\"],\n                onUpdate: {\n                    \"LandingPage.useEffect\": ()=>console.log('🔄 New content available')\n                }[\"LandingPage.useEffect\"],\n                onError: {\n                    \"LandingPage.useEffect\": (error)=>console.warn('⚠️ Service Worker registration failed:', error)\n                }[\"LandingPage.useEffect\"]\n            });\n            // Progressive loading: show critical content first\n            setIsLoaded(true);\n            // Load non-critical sections after a short delay\n            const timer = setTimeout({\n                \"LandingPage.useEffect.timer\": ()=>{\n                    setSectionsLoaded(true);\n                }\n            }[\"LandingPage.useEffect.timer\"], 100);\n            return ({\n                \"LandingPage.useEffect\": ()=>clearTimeout(timer)\n            })[\"LandingPage.useEffect\"];\n        }\n    }[\"LandingPage.useEffect\"], []);\n    // Prefetch likely next pages on hover\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LandingPage.useEffect\": ()=>{\n            if (true) {\n                const prefetchPages = {\n                    \"LandingPage.useEffect.prefetchPages\": ()=>{\n                        // Prefetch auth pages since users likely to sign up\n                        __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_app_auth_signup_page_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/app/auth/signup/page */ \"(app-pages-browser)/./src/app/auth/signup/page.tsx\"));\n                        __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_app_pricing_page_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/app/pricing/page */ \"(app-pages-browser)/./src/app/pricing/page.tsx\"));\n                        __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_app_features_page_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/app/features/page */ \"(app-pages-browser)/./src/app/features/page.tsx\"));\n                    }\n                }[\"LandingPage.useEffect.prefetchPages\"];\n                // Prefetch after initial load\n                const prefetchTimer = setTimeout(prefetchPages, 2000);\n                return ({\n                    \"LandingPage.useEffect\": ()=>clearTimeout(prefetchTimer)\n                })[\"LandingPage.useEffect\"];\n            }\n        }\n    }[\"LandingPage.useEffect\"], []);\n    if (!isLoaded) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-blue-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-[#ff6b35] mx-auto mb-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 text-sm\",\n                        children: \"Loading RouKey...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 73,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 72,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-black\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_HeroSection__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_TrustBadges__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, this),\n                    sectionsLoaded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n                        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SectionSkeleton, {}, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 31\n                        }, void 0),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RoutingVisualization, {}, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SectionSkeleton, {}, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 11\n                    }, this),\n                    sectionsLoaded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n                        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SectionSkeleton, {}, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 31\n                        }, void 0),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeaturesSection, {}, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SectionSkeleton, {}, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 11\n                    }, this),\n                    sectionsLoaded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n                        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SectionSkeleton, {}, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 31\n                        }, void 0),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TestimonialsSection, {}, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SectionSkeleton, {}, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 11\n                    }, this),\n                    sectionsLoaded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n                        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SectionSkeleton, {}, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 31\n                        }, void 0),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CTASection, {}, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SectionSkeleton, {}, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, this),\n            sectionsLoaded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n                fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-32 bg-gray-900\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 29\n                }, void 0),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Footer, {}, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 125,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-32 bg-gray-900\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 129,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 82,\n        columnNumber: 5\n    }, this);\n}\n_s(LandingPage, \"T9TBDw6sNFIPQ1pRnh0ENfNdDk8=\");\n_c6 = LandingPage;\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"FeaturesSection\");\n$RefreshReg$(_c1, \"RoutingVisualization\");\n$RefreshReg$(_c2, \"TestimonialsSection\");\n$RefreshReg$(_c3, \"CTASection\");\n$RefreshReg$(_c4, \"Footer\");\n$RefreshReg$(_c5, \"SectionSkeleton\");\n$RefreshReg$(_c6, \"LandingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/utils/serviceWorker.ts":
/*!************************************!*\
  !*** ./src/utils/serviceWorker.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearAllCaches: () => (/* binding */ clearAllCaches),\n/* harmony export */   isServiceWorkerActive: () => (/* binding */ isServiceWorkerActive),\n/* harmony export */   isServiceWorkerSupported: () => (/* binding */ isServiceWorkerSupported),\n/* harmony export */   preloadCriticalResources: () => (/* binding */ preloadCriticalResources),\n/* harmony export */   registerServiceWorker: () => (/* binding */ registerServiceWorker),\n/* harmony export */   sendMessageToServiceWorker: () => (/* binding */ sendMessageToServiceWorker),\n/* harmony export */   unregisterServiceWorker: () => (/* binding */ unregisterServiceWorker),\n/* harmony export */   updateServiceWorker: () => (/* binding */ updateServiceWorker)\n/* harmony export */ });\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n// Service Worker Registration and Management\n/* __next_internal_client_entry_do_not_use__ registerServiceWorker,unregisterServiceWorker,clearAllCaches,preloadCriticalResources,isServiceWorkerSupported,isServiceWorkerActive,updateServiceWorker,sendMessageToServiceWorker auto */ const isLocalhost = Boolean( true && (window.location.hostname === 'localhost' || window.location.hostname === '[::1]' || window.location.hostname.match(/^127(?:\\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}$/)));\nfunction registerServiceWorker(config) {\n    if (false) {}\n    if ('serviceWorker' in navigator) {\n        const publicUrl = new URL(process.env.PUBLIC_URL || '', window.location.href);\n        if (publicUrl.origin !== window.location.origin) {\n            return;\n        }\n        window.addEventListener('load', ()=>{\n            const swUrl = '/sw.js';\n            if (isLocalhost) {\n                checkValidServiceWorker(swUrl, config);\n                navigator.serviceWorker.ready.then(()=>{\n                    console.log('🔧 Service Worker ready in development mode');\n                });\n            } else {\n                registerValidSW(swUrl, config);\n            }\n        });\n    }\n}\nfunction registerValidSW(swUrl, config) {\n    navigator.serviceWorker.register(swUrl).then((registration)=>{\n        console.log('✅ Service Worker registered successfully');\n        registration.onupdatefound = ()=>{\n            const installingWorker = registration.installing;\n            if (installingWorker == null) {\n                return;\n            }\n            installingWorker.onstatechange = ()=>{\n                if (installingWorker.state === 'installed') {\n                    if (navigator.serviceWorker.controller) {\n                        console.log('🔄 New content available, will be used when all tabs are closed');\n                        if (config && config.onUpdate) {\n                            config.onUpdate(registration);\n                        }\n                    } else {\n                        console.log('📦 Content cached for offline use');\n                        if (config && config.onSuccess) {\n                            config.onSuccess(registration);\n                        }\n                    }\n                }\n            };\n        };\n    }).catch((error)=>{\n        console.error('❌ Service Worker registration failed:', error);\n        if (config && config.onError) {\n            config.onError(error);\n        }\n    });\n}\nfunction checkValidServiceWorker(swUrl, config) {\n    fetch(swUrl, {\n        headers: {\n            'Service-Worker': 'script'\n        }\n    }).then((response)=>{\n        const contentType = response.headers.get('content-type');\n        if (response.status === 404 || contentType != null && contentType.indexOf('javascript') === -1) {\n            navigator.serviceWorker.ready.then((registration)=>{\n                registration.unregister().then(()=>{\n                    window.location.reload();\n                });\n            });\n        } else {\n            registerValidSW(swUrl, config);\n        }\n    }).catch(()=>{\n        console.log('🔌 No internet connection found. App is running in offline mode.');\n    });\n}\nfunction unregisterServiceWorker() {\n    if ('serviceWorker' in navigator) {\n        navigator.serviceWorker.ready.then((registration)=>{\n            registration.unregister();\n            console.log('🗑️ Service Worker unregistered');\n        }).catch((error)=>{\n            console.error('❌ Service Worker unregistration failed:', error);\n        });\n    }\n}\n// Utility to clear all caches\nfunction clearAllCaches() {\n    if ('caches' in window) {\n        return caches.keys().then((cacheNames)=>{\n            return Promise.all(cacheNames.map((cacheName)=>{\n                console.log('🗑️ Clearing cache:', cacheName);\n                return caches.delete(cacheName);\n            }));\n        });\n    }\n    return Promise.resolve();\n}\n// Utility to preload critical resources\nfunction preloadCriticalResources() {\n    const criticalRoutes = [\n        '/dashboard',\n        '/playground',\n        '/logs',\n        '/my-models',\n        '/routing-setup'\n    ];\n    const criticalAPIs = [\n        '/api/custom-configs',\n        '/api/system-status',\n        '/api/analytics/summary'\n    ];\n    // Preload routes\n    criticalRoutes.forEach((route)=>{\n        const link = document.createElement('link');\n        link.rel = 'prefetch';\n        link.href = route;\n        document.head.appendChild(link);\n    });\n    // Preload API data\n    criticalAPIs.forEach((api)=>{\n        fetch(api, {\n            method: 'GET',\n            headers: {\n                'X-Prefetch': 'true'\n            }\n        }).catch(()=>{\n        // Ignore prefetch errors\n        });\n    });\n}\n// Check if service worker is supported and active\nfunction isServiceWorkerSupported() {\n    return 'serviceWorker' in navigator;\n}\nfunction isServiceWorkerActive() {\n    if (!isServiceWorkerSupported()) {\n        return Promise.resolve(false);\n    }\n    return navigator.serviceWorker.ready.then((registration)=>{\n        return registration.active !== null;\n    });\n}\n// Force service worker update\nfunction updateServiceWorker() {\n    if ('serviceWorker' in navigator) {\n        navigator.serviceWorker.ready.then((registration)=>{\n            registration.update();\n            console.log('🔄 Service Worker update requested');\n        });\n    }\n}\n// Send message to service worker\nfunction sendMessageToServiceWorker(message) {\n    if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {\n        navigator.serviceWorker.controller.postMessage(message);\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy91dGlscy9zZXJ2aWNlV29ya2VyLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUFBLDZDQUE2Qzt5T0FTN0MsTUFBTUEsY0FBY0MsUUFDbEIsS0FBNkIsSUFDNUJDLENBQUFBLE9BQU9DLFFBQVEsQ0FBQ0MsUUFBUSxLQUFLLGVBQzVCRixPQUFPQyxRQUFRLENBQUNDLFFBQVEsS0FBSyxXQUM3QkYsT0FBT0MsUUFBUSxDQUFDQyxRQUFRLENBQUNDLEtBQUssQ0FBQyx5REFBd0Q7QUFHcEYsU0FBU0Msc0JBQXNCQyxNQUE0QjtJQUNoRSxJQUFJLEtBQTZCLEVBQUUsRUFBTztJQUUxQyxJQUFJLG1CQUFtQkMsV0FBVztRQUNoQyxNQUFNQyxZQUFZLElBQUlDLElBQUlDLE9BQU9BLENBQUNDLEdBQUcsQ0FBQ0MsVUFBVSxJQUFJLElBQUlYLE9BQU9DLFFBQVEsQ0FBQ1csSUFBSTtRQUM1RSxJQUFJTCxVQUFVTSxNQUFNLEtBQUtiLE9BQU9DLFFBQVEsQ0FBQ1ksTUFBTSxFQUFFO1lBQy9DO1FBQ0Y7UUFFQWIsT0FBT2MsZ0JBQWdCLENBQUMsUUFBUTtZQUM5QixNQUFNQyxRQUFRO1lBRWQsSUFBSWpCLGFBQWE7Z0JBQ2ZrQix3QkFBd0JELE9BQU9WO2dCQUMvQkMsVUFBVVcsYUFBYSxDQUFDQyxLQUFLLENBQUNDLElBQUksQ0FBQztvQkFDakNDLFFBQVFDLEdBQUcsQ0FBQztnQkFDZDtZQUNGLE9BQU87Z0JBQ0xDLGdCQUFnQlAsT0FBT1Y7WUFDekI7UUFDRjtJQUNGO0FBQ0Y7QUFFQSxTQUFTaUIsZ0JBQWdCUCxLQUFhLEVBQUVWLE1BQTRCO0lBQ2xFQyxVQUFVVyxhQUFhLENBQ3BCTSxRQUFRLENBQUNSLE9BQ1RJLElBQUksQ0FBQyxDQUFDSztRQUNMSixRQUFRQyxHQUFHLENBQUM7UUFFWkcsYUFBYUMsYUFBYSxHQUFHO1lBQzNCLE1BQU1DLG1CQUFtQkYsYUFBYUcsVUFBVTtZQUNoRCxJQUFJRCxvQkFBb0IsTUFBTTtnQkFDNUI7WUFDRjtZQUVBQSxpQkFBaUJFLGFBQWEsR0FBRztnQkFDL0IsSUFBSUYsaUJBQWlCRyxLQUFLLEtBQUssYUFBYTtvQkFDMUMsSUFBSXZCLFVBQVVXLGFBQWEsQ0FBQ2EsVUFBVSxFQUFFO3dCQUN0Q1YsUUFBUUMsR0FBRyxDQUFDO3dCQUNaLElBQUloQixVQUFVQSxPQUFPMEIsUUFBUSxFQUFFOzRCQUM3QjFCLE9BQU8wQixRQUFRLENBQUNQO3dCQUNsQjtvQkFDRixPQUFPO3dCQUNMSixRQUFRQyxHQUFHLENBQUM7d0JBQ1osSUFBSWhCLFVBQVVBLE9BQU8yQixTQUFTLEVBQUU7NEJBQzlCM0IsT0FBTzJCLFNBQVMsQ0FBQ1I7d0JBQ25CO29CQUNGO2dCQUNGO1lBQ0Y7UUFDRjtJQUNGLEdBQ0NTLEtBQUssQ0FBQyxDQUFDQztRQUNOZCxRQUFRYyxLQUFLLENBQUMseUNBQXlDQTtRQUN2RCxJQUFJN0IsVUFBVUEsT0FBTzhCLE9BQU8sRUFBRTtZQUM1QjlCLE9BQU84QixPQUFPLENBQUNEO1FBQ2pCO0lBQ0Y7QUFDSjtBQUVBLFNBQVNsQix3QkFBd0JELEtBQWEsRUFBRVYsTUFBNEI7SUFDMUUrQixNQUFNckIsT0FBTztRQUNYc0IsU0FBUztZQUFFLGtCQUFrQjtRQUFTO0lBQ3hDLEdBQ0dsQixJQUFJLENBQUMsQ0FBQ21CO1FBQ0wsTUFBTUMsY0FBY0QsU0FBU0QsT0FBTyxDQUFDRyxHQUFHLENBQUM7UUFDekMsSUFDRUYsU0FBU0csTUFBTSxLQUFLLE9BQ25CRixlQUFlLFFBQVFBLFlBQVlHLE9BQU8sQ0FBQyxrQkFBa0IsQ0FBQyxHQUMvRDtZQUNBcEMsVUFBVVcsYUFBYSxDQUFDQyxLQUFLLENBQUNDLElBQUksQ0FBQyxDQUFDSztnQkFDbENBLGFBQWFtQixVQUFVLEdBQUd4QixJQUFJLENBQUM7b0JBQzdCbkIsT0FBT0MsUUFBUSxDQUFDMkMsTUFBTTtnQkFDeEI7WUFDRjtRQUNGLE9BQU87WUFDTHRCLGdCQUFnQlAsT0FBT1Y7UUFDekI7SUFDRixHQUNDNEIsS0FBSyxDQUFDO1FBQ0xiLFFBQVFDLEdBQUcsQ0FBQztJQUNkO0FBQ0o7QUFFTyxTQUFTd0I7SUFDZCxJQUFJLG1CQUFtQnZDLFdBQVc7UUFDaENBLFVBQVVXLGFBQWEsQ0FBQ0MsS0FBSyxDQUMxQkMsSUFBSSxDQUFDLENBQUNLO1lBQ0xBLGFBQWFtQixVQUFVO1lBQ3ZCdkIsUUFBUUMsR0FBRyxDQUFDO1FBQ2QsR0FDQ1ksS0FBSyxDQUFDLENBQUNDO1lBQ05kLFFBQVFjLEtBQUssQ0FBQywyQ0FBMkNBO1FBQzNEO0lBQ0o7QUFDRjtBQUVBLDhCQUE4QjtBQUN2QixTQUFTWTtJQUNkLElBQUksWUFBWTlDLFFBQVE7UUFDdEIsT0FBTytDLE9BQU9DLElBQUksR0FBRzdCLElBQUksQ0FBQyxDQUFDOEI7WUFDekIsT0FBT0MsUUFBUUMsR0FBRyxDQUNoQkYsV0FBV0csR0FBRyxDQUFDLENBQUNDO2dCQUNkakMsUUFBUUMsR0FBRyxDQUFDLHVCQUF1QmdDO2dCQUNuQyxPQUFPTixPQUFPTyxNQUFNLENBQUNEO1lBQ3ZCO1FBRUo7SUFDRjtJQUNBLE9BQU9ILFFBQVFLLE9BQU87QUFDeEI7QUFFQSx3Q0FBd0M7QUFDakMsU0FBU0M7SUFDZCxNQUFNQyxpQkFBaUI7UUFDckI7UUFDQTtRQUNBO1FBQ0E7UUFDQTtLQUNEO0lBRUQsTUFBTUMsZUFBZTtRQUNuQjtRQUNBO1FBQ0E7S0FDRDtJQUVELGlCQUFpQjtJQUNqQkQsZUFBZUUsT0FBTyxDQUFDLENBQUNDO1FBQ3RCLE1BQU1DLE9BQU9DLFNBQVNDLGFBQWEsQ0FBQztRQUNwQ0YsS0FBS0csR0FBRyxHQUFHO1FBQ1hILEtBQUtqRCxJQUFJLEdBQUdnRDtRQUNaRSxTQUFTRyxJQUFJLENBQUNDLFdBQVcsQ0FBQ0w7SUFDNUI7SUFFQSxtQkFBbUI7SUFDbkJILGFBQWFDLE9BQU8sQ0FBQyxDQUFDUTtRQUNwQi9CLE1BQU0rQixLQUFLO1lBQ1RDLFFBQVE7WUFDUi9CLFNBQVM7Z0JBQUUsY0FBYztZQUFPO1FBQ2xDLEdBQUdKLEtBQUssQ0FBQztRQUNQLHlCQUF5QjtRQUMzQjtJQUNGO0FBQ0Y7QUFFQSxrREFBa0Q7QUFDM0MsU0FBU29DO0lBQ2QsT0FBTyxtQkFBbUIvRDtBQUM1QjtBQUVPLFNBQVNnRTtJQUNkLElBQUksQ0FBQ0QsNEJBQTRCO1FBQy9CLE9BQU9uQixRQUFRSyxPQUFPLENBQUM7SUFDekI7SUFFQSxPQUFPakQsVUFBVVcsYUFBYSxDQUFDQyxLQUFLLENBQUNDLElBQUksQ0FBQyxDQUFDSztRQUN6QyxPQUFPQSxhQUFhK0MsTUFBTSxLQUFLO0lBQ2pDO0FBQ0Y7QUFFQSw4QkFBOEI7QUFDdkIsU0FBU0M7SUFDZCxJQUFJLG1CQUFtQmxFLFdBQVc7UUFDaENBLFVBQVVXLGFBQWEsQ0FBQ0MsS0FBSyxDQUFDQyxJQUFJLENBQUMsQ0FBQ0s7WUFDbENBLGFBQWFpRCxNQUFNO1lBQ25CckQsUUFBUUMsR0FBRyxDQUFDO1FBQ2Q7SUFDRjtBQUNGO0FBRUEsaUNBQWlDO0FBQzFCLFNBQVNxRCwyQkFBMkJDLE9BQVk7SUFDckQsSUFBSSxtQkFBbUJyRSxhQUFhQSxVQUFVVyxhQUFhLENBQUNhLFVBQVUsRUFBRTtRQUN0RXhCLFVBQVVXLGFBQWEsQ0FBQ2EsVUFBVSxDQUFDOEMsV0FBVyxDQUFDRDtJQUNqRDtBQUNGIiwic291cmNlcyI6WyJDOlxcUm9LZXkgQXBwXFxyb2tleS1hcHBcXHNyY1xcdXRpbHNcXHNlcnZpY2VXb3JrZXIudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gU2VydmljZSBXb3JrZXIgUmVnaXN0cmF0aW9uIGFuZCBNYW5hZ2VtZW50XG4ndXNlIGNsaWVudCc7XG5cbmludGVyZmFjZSBTZXJ2aWNlV29ya2VyQ29uZmlnIHtcbiAgb25VcGRhdGU/OiAocmVnaXN0cmF0aW9uOiBTZXJ2aWNlV29ya2VyUmVnaXN0cmF0aW9uKSA9PiB2b2lkO1xuICBvblN1Y2Nlc3M/OiAocmVnaXN0cmF0aW9uOiBTZXJ2aWNlV29ya2VyUmVnaXN0cmF0aW9uKSA9PiB2b2lkO1xuICBvbkVycm9yPzogKGVycm9yOiBFcnJvcikgPT4gdm9pZDtcbn1cblxuY29uc3QgaXNMb2NhbGhvc3QgPSBCb29sZWFuKFxuICB0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJyAmJlxuICAod2luZG93LmxvY2F0aW9uLmhvc3RuYW1lID09PSAnbG9jYWxob3N0JyB8fFxuICAgIHdpbmRvdy5sb2NhdGlvbi5ob3N0bmFtZSA9PT0gJ1s6OjFdJyB8fFxuICAgIHdpbmRvdy5sb2NhdGlvbi5ob3N0bmFtZS5tYXRjaCgvXjEyNyg/OlxcLig/OjI1WzAtNV18MlswLTRdWzAtOV18WzAxXT9bMC05XVswLTldPykpezN9JC8pKVxuKTtcblxuZXhwb3J0IGZ1bmN0aW9uIHJlZ2lzdGVyU2VydmljZVdvcmtlcihjb25maWc/OiBTZXJ2aWNlV29ya2VyQ29uZmlnKSB7XG4gIGlmICh0eXBlb2Ygd2luZG93ID09PSAndW5kZWZpbmVkJykgcmV0dXJuO1xuXG4gIGlmICgnc2VydmljZVdvcmtlcicgaW4gbmF2aWdhdG9yKSB7XG4gICAgY29uc3QgcHVibGljVXJsID0gbmV3IFVSTChwcm9jZXNzLmVudi5QVUJMSUNfVVJMIHx8ICcnLCB3aW5kb3cubG9jYXRpb24uaHJlZik7XG4gICAgaWYgKHB1YmxpY1VybC5vcmlnaW4gIT09IHdpbmRvdy5sb2NhdGlvbi5vcmlnaW4pIHtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcignbG9hZCcsICgpID0+IHtcbiAgICAgIGNvbnN0IHN3VXJsID0gJy9zdy5qcyc7XG5cbiAgICAgIGlmIChpc0xvY2FsaG9zdCkge1xuICAgICAgICBjaGVja1ZhbGlkU2VydmljZVdvcmtlcihzd1VybCwgY29uZmlnKTtcbiAgICAgICAgbmF2aWdhdG9yLnNlcnZpY2VXb3JrZXIucmVhZHkudGhlbigoKSA9PiB7XG4gICAgICAgICAgY29uc29sZS5sb2coJ/CflKcgU2VydmljZSBXb3JrZXIgcmVhZHkgaW4gZGV2ZWxvcG1lbnQgbW9kZScpO1xuICAgICAgICB9KTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHJlZ2lzdGVyVmFsaWRTVyhzd1VybCwgY29uZmlnKTtcbiAgICAgIH1cbiAgICB9KTtcbiAgfVxufVxuXG5mdW5jdGlvbiByZWdpc3RlclZhbGlkU1coc3dVcmw6IHN0cmluZywgY29uZmlnPzogU2VydmljZVdvcmtlckNvbmZpZykge1xuICBuYXZpZ2F0b3Iuc2VydmljZVdvcmtlclxuICAgIC5yZWdpc3Rlcihzd1VybClcbiAgICAudGhlbigocmVnaXN0cmF0aW9uKSA9PiB7XG4gICAgICBjb25zb2xlLmxvZygn4pyFIFNlcnZpY2UgV29ya2VyIHJlZ2lzdGVyZWQgc3VjY2Vzc2Z1bGx5Jyk7XG4gICAgICBcbiAgICAgIHJlZ2lzdHJhdGlvbi5vbnVwZGF0ZWZvdW5kID0gKCkgPT4ge1xuICAgICAgICBjb25zdCBpbnN0YWxsaW5nV29ya2VyID0gcmVnaXN0cmF0aW9uLmluc3RhbGxpbmc7XG4gICAgICAgIGlmIChpbnN0YWxsaW5nV29ya2VyID09IG51bGwpIHtcbiAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cbiAgICAgICAgXG4gICAgICAgIGluc3RhbGxpbmdXb3JrZXIub25zdGF0ZWNoYW5nZSA9ICgpID0+IHtcbiAgICAgICAgICBpZiAoaW5zdGFsbGluZ1dvcmtlci5zdGF0ZSA9PT0gJ2luc3RhbGxlZCcpIHtcbiAgICAgICAgICAgIGlmIChuYXZpZ2F0b3Iuc2VydmljZVdvcmtlci5jb250cm9sbGVyKSB7XG4gICAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5SEIE5ldyBjb250ZW50IGF2YWlsYWJsZSwgd2lsbCBiZSB1c2VkIHdoZW4gYWxsIHRhYnMgYXJlIGNsb3NlZCcpO1xuICAgICAgICAgICAgICBpZiAoY29uZmlnICYmIGNvbmZpZy5vblVwZGF0ZSkge1xuICAgICAgICAgICAgICAgIGNvbmZpZy5vblVwZGF0ZShyZWdpc3RyYXRpb24pO1xuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICBjb25zb2xlLmxvZygn8J+TpiBDb250ZW50IGNhY2hlZCBmb3Igb2ZmbGluZSB1c2UnKTtcbiAgICAgICAgICAgICAgaWYgKGNvbmZpZyAmJiBjb25maWcub25TdWNjZXNzKSB7XG4gICAgICAgICAgICAgICAgY29uZmlnLm9uU3VjY2VzcyhyZWdpc3RyYXRpb24pO1xuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfVxuICAgICAgICB9O1xuICAgICAgfTtcbiAgICB9KVxuICAgIC5jYXRjaCgoZXJyb3IpID0+IHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBTZXJ2aWNlIFdvcmtlciByZWdpc3RyYXRpb24gZmFpbGVkOicsIGVycm9yKTtcbiAgICAgIGlmIChjb25maWcgJiYgY29uZmlnLm9uRXJyb3IpIHtcbiAgICAgICAgY29uZmlnLm9uRXJyb3IoZXJyb3IpO1xuICAgICAgfVxuICAgIH0pO1xufVxuXG5mdW5jdGlvbiBjaGVja1ZhbGlkU2VydmljZVdvcmtlcihzd1VybDogc3RyaW5nLCBjb25maWc/OiBTZXJ2aWNlV29ya2VyQ29uZmlnKSB7XG4gIGZldGNoKHN3VXJsLCB7XG4gICAgaGVhZGVyczogeyAnU2VydmljZS1Xb3JrZXInOiAnc2NyaXB0JyB9LFxuICB9KVxuICAgIC50aGVuKChyZXNwb25zZSkgPT4ge1xuICAgICAgY29uc3QgY29udGVudFR5cGUgPSByZXNwb25zZS5oZWFkZXJzLmdldCgnY29udGVudC10eXBlJyk7XG4gICAgICBpZiAoXG4gICAgICAgIHJlc3BvbnNlLnN0YXR1cyA9PT0gNDA0IHx8XG4gICAgICAgIChjb250ZW50VHlwZSAhPSBudWxsICYmIGNvbnRlbnRUeXBlLmluZGV4T2YoJ2phdmFzY3JpcHQnKSA9PT0gLTEpXG4gICAgICApIHtcbiAgICAgICAgbmF2aWdhdG9yLnNlcnZpY2VXb3JrZXIucmVhZHkudGhlbigocmVnaXN0cmF0aW9uKSA9PiB7XG4gICAgICAgICAgcmVnaXN0cmF0aW9uLnVucmVnaXN0ZXIoKS50aGVuKCgpID0+IHtcbiAgICAgICAgICAgIHdpbmRvdy5sb2NhdGlvbi5yZWxvYWQoKTtcbiAgICAgICAgICB9KTtcbiAgICAgICAgfSk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICByZWdpc3RlclZhbGlkU1coc3dVcmwsIGNvbmZpZyk7XG4gICAgICB9XG4gICAgfSlcbiAgICAuY2F0Y2goKCkgPT4ge1xuICAgICAgY29uc29sZS5sb2coJ/CflIwgTm8gaW50ZXJuZXQgY29ubmVjdGlvbiBmb3VuZC4gQXBwIGlzIHJ1bm5pbmcgaW4gb2ZmbGluZSBtb2RlLicpO1xuICAgIH0pO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gdW5yZWdpc3RlclNlcnZpY2VXb3JrZXIoKSB7XG4gIGlmICgnc2VydmljZVdvcmtlcicgaW4gbmF2aWdhdG9yKSB7XG4gICAgbmF2aWdhdG9yLnNlcnZpY2VXb3JrZXIucmVhZHlcbiAgICAgIC50aGVuKChyZWdpc3RyYXRpb24pID0+IHtcbiAgICAgICAgcmVnaXN0cmF0aW9uLnVucmVnaXN0ZXIoKTtcbiAgICAgICAgY29uc29sZS5sb2coJ/Cfl5HvuI8gU2VydmljZSBXb3JrZXIgdW5yZWdpc3RlcmVkJyk7XG4gICAgICB9KVxuICAgICAgLmNhdGNoKChlcnJvcikgPT4ge1xuICAgICAgICBjb25zb2xlLmVycm9yKCfinYwgU2VydmljZSBXb3JrZXIgdW5yZWdpc3RyYXRpb24gZmFpbGVkOicsIGVycm9yKTtcbiAgICAgIH0pO1xuICB9XG59XG5cbi8vIFV0aWxpdHkgdG8gY2xlYXIgYWxsIGNhY2hlc1xuZXhwb3J0IGZ1bmN0aW9uIGNsZWFyQWxsQ2FjaGVzKCkge1xuICBpZiAoJ2NhY2hlcycgaW4gd2luZG93KSB7XG4gICAgcmV0dXJuIGNhY2hlcy5rZXlzKCkudGhlbigoY2FjaGVOYW1lcykgPT4ge1xuICAgICAgcmV0dXJuIFByb21pc2UuYWxsKFxuICAgICAgICBjYWNoZU5hbWVzLm1hcCgoY2FjaGVOYW1lKSA9PiB7XG4gICAgICAgICAgY29uc29sZS5sb2coJ/Cfl5HvuI8gQ2xlYXJpbmcgY2FjaGU6JywgY2FjaGVOYW1lKTtcbiAgICAgICAgICByZXR1cm4gY2FjaGVzLmRlbGV0ZShjYWNoZU5hbWUpO1xuICAgICAgICB9KVxuICAgICAgKTtcbiAgICB9KTtcbiAgfVxuICByZXR1cm4gUHJvbWlzZS5yZXNvbHZlKCk7XG59XG5cbi8vIFV0aWxpdHkgdG8gcHJlbG9hZCBjcml0aWNhbCByZXNvdXJjZXNcbmV4cG9ydCBmdW5jdGlvbiBwcmVsb2FkQ3JpdGljYWxSZXNvdXJjZXMoKSB7XG4gIGNvbnN0IGNyaXRpY2FsUm91dGVzID0gW1xuICAgICcvZGFzaGJvYXJkJyxcbiAgICAnL3BsYXlncm91bmQnLFxuICAgICcvbG9ncycsXG4gICAgJy9teS1tb2RlbHMnLFxuICAgICcvcm91dGluZy1zZXR1cCdcbiAgXTtcblxuICBjb25zdCBjcml0aWNhbEFQSXMgPSBbXG4gICAgJy9hcGkvY3VzdG9tLWNvbmZpZ3MnLFxuICAgICcvYXBpL3N5c3RlbS1zdGF0dXMnLFxuICAgICcvYXBpL2FuYWx5dGljcy9zdW1tYXJ5J1xuICBdO1xuXG4gIC8vIFByZWxvYWQgcm91dGVzXG4gIGNyaXRpY2FsUm91dGVzLmZvckVhY2goKHJvdXRlKSA9PiB7XG4gICAgY29uc3QgbGluayA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2xpbmsnKTtcbiAgICBsaW5rLnJlbCA9ICdwcmVmZXRjaCc7XG4gICAgbGluay5ocmVmID0gcm91dGU7XG4gICAgZG9jdW1lbnQuaGVhZC5hcHBlbmRDaGlsZChsaW5rKTtcbiAgfSk7XG5cbiAgLy8gUHJlbG9hZCBBUEkgZGF0YVxuICBjcml0aWNhbEFQSXMuZm9yRWFjaCgoYXBpKSA9PiB7XG4gICAgZmV0Y2goYXBpLCB7IFxuICAgICAgbWV0aG9kOiAnR0VUJyxcbiAgICAgIGhlYWRlcnM6IHsgJ1gtUHJlZmV0Y2gnOiAndHJ1ZScgfVxuICAgIH0pLmNhdGNoKCgpID0+IHtcbiAgICAgIC8vIElnbm9yZSBwcmVmZXRjaCBlcnJvcnNcbiAgICB9KTtcbiAgfSk7XG59XG5cbi8vIENoZWNrIGlmIHNlcnZpY2Ugd29ya2VyIGlzIHN1cHBvcnRlZCBhbmQgYWN0aXZlXG5leHBvcnQgZnVuY3Rpb24gaXNTZXJ2aWNlV29ya2VyU3VwcG9ydGVkKCk6IGJvb2xlYW4ge1xuICByZXR1cm4gJ3NlcnZpY2VXb3JrZXInIGluIG5hdmlnYXRvcjtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGlzU2VydmljZVdvcmtlckFjdGl2ZSgpOiBQcm9taXNlPGJvb2xlYW4+IHtcbiAgaWYgKCFpc1NlcnZpY2VXb3JrZXJTdXBwb3J0ZWQoKSkge1xuICAgIHJldHVybiBQcm9taXNlLnJlc29sdmUoZmFsc2UpO1xuICB9XG5cbiAgcmV0dXJuIG5hdmlnYXRvci5zZXJ2aWNlV29ya2VyLnJlYWR5LnRoZW4oKHJlZ2lzdHJhdGlvbikgPT4ge1xuICAgIHJldHVybiByZWdpc3RyYXRpb24uYWN0aXZlICE9PSBudWxsO1xuICB9KTtcbn1cblxuLy8gRm9yY2Ugc2VydmljZSB3b3JrZXIgdXBkYXRlXG5leHBvcnQgZnVuY3Rpb24gdXBkYXRlU2VydmljZVdvcmtlcigpIHtcbiAgaWYgKCdzZXJ2aWNlV29ya2VyJyBpbiBuYXZpZ2F0b3IpIHtcbiAgICBuYXZpZ2F0b3Iuc2VydmljZVdvcmtlci5yZWFkeS50aGVuKChyZWdpc3RyYXRpb24pID0+IHtcbiAgICAgIHJlZ2lzdHJhdGlvbi51cGRhdGUoKTtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5SEIFNlcnZpY2UgV29ya2VyIHVwZGF0ZSByZXF1ZXN0ZWQnKTtcbiAgICB9KTtcbiAgfVxufVxuXG4vLyBTZW5kIG1lc3NhZ2UgdG8gc2VydmljZSB3b3JrZXJcbmV4cG9ydCBmdW5jdGlvbiBzZW5kTWVzc2FnZVRvU2VydmljZVdvcmtlcihtZXNzYWdlOiBhbnkpIHtcbiAgaWYgKCdzZXJ2aWNlV29ya2VyJyBpbiBuYXZpZ2F0b3IgJiYgbmF2aWdhdG9yLnNlcnZpY2VXb3JrZXIuY29udHJvbGxlcikge1xuICAgIG5hdmlnYXRvci5zZXJ2aWNlV29ya2VyLmNvbnRyb2xsZXIucG9zdE1lc3NhZ2UobWVzc2FnZSk7XG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJpc0xvY2FsaG9zdCIsIkJvb2xlYW4iLCJ3aW5kb3ciLCJsb2NhdGlvbiIsImhvc3RuYW1lIiwibWF0Y2giLCJyZWdpc3RlclNlcnZpY2VXb3JrZXIiLCJjb25maWciLCJuYXZpZ2F0b3IiLCJwdWJsaWNVcmwiLCJVUkwiLCJwcm9jZXNzIiwiZW52IiwiUFVCTElDX1VSTCIsImhyZWYiLCJvcmlnaW4iLCJhZGRFdmVudExpc3RlbmVyIiwic3dVcmwiLCJjaGVja1ZhbGlkU2VydmljZVdvcmtlciIsInNlcnZpY2VXb3JrZXIiLCJyZWFkeSIsInRoZW4iLCJjb25zb2xlIiwibG9nIiwicmVnaXN0ZXJWYWxpZFNXIiwicmVnaXN0ZXIiLCJyZWdpc3RyYXRpb24iLCJvbnVwZGF0ZWZvdW5kIiwiaW5zdGFsbGluZ1dvcmtlciIsImluc3RhbGxpbmciLCJvbnN0YXRlY2hhbmdlIiwic3RhdGUiLCJjb250cm9sbGVyIiwib25VcGRhdGUiLCJvblN1Y2Nlc3MiLCJjYXRjaCIsImVycm9yIiwib25FcnJvciIsImZldGNoIiwiaGVhZGVycyIsInJlc3BvbnNlIiwiY29udGVudFR5cGUiLCJnZXQiLCJzdGF0dXMiLCJpbmRleE9mIiwidW5yZWdpc3RlciIsInJlbG9hZCIsInVucmVnaXN0ZXJTZXJ2aWNlV29ya2VyIiwiY2xlYXJBbGxDYWNoZXMiLCJjYWNoZXMiLCJrZXlzIiwiY2FjaGVOYW1lcyIsIlByb21pc2UiLCJhbGwiLCJtYXAiLCJjYWNoZU5hbWUiLCJkZWxldGUiLCJyZXNvbHZlIiwicHJlbG9hZENyaXRpY2FsUmVzb3VyY2VzIiwiY3JpdGljYWxSb3V0ZXMiLCJjcml0aWNhbEFQSXMiLCJmb3JFYWNoIiwicm91dGUiLCJsaW5rIiwiZG9jdW1lbnQiLCJjcmVhdGVFbGVtZW50IiwicmVsIiwiaGVhZCIsImFwcGVuZENoaWxkIiwiYXBpIiwibWV0aG9kIiwiaXNTZXJ2aWNlV29ya2VyU3VwcG9ydGVkIiwiaXNTZXJ2aWNlV29ya2VyQWN0aXZlIiwiYWN0aXZlIiwidXBkYXRlU2VydmljZVdvcmtlciIsInVwZGF0ZSIsInNlbmRNZXNzYWdlVG9TZXJ2aWNlV29ya2VyIiwibWVzc2FnZSIsInBvc3RNZXNzYWdlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/serviceWorker.ts\n"));

/***/ })

});