"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/pricing/page",{

/***/ "(app-pages-browser)/./node_modules/next/dist/api/navigation.js":
/*!**************************************************!*\
  !*** ./node_modules/next/dist/api/navigation.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../client/components/navigation */ \"(app-pages-browser)/./node_modules/next/dist/client/components/navigation.js\");\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_client_components_navigation__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n//# sourceMappingURL=navigation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL25hdmlnYXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWdEOztBQUVoRCIsInNvdXJjZXMiOlsiQzpcXFJvS2V5IEFwcFxccm9rZXktYXBwXFxub2RlX21vZHVsZXNcXG5leHRcXGRpc3RcXGFwaVxcbmF2aWdhdGlvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tICcuLi9jbGllbnQvY29tcG9uZW50cy9uYXZpZ2F0aW9uJztcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bmF2aWdhdGlvbi5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/api/navigation.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/OptimisticLink.tsx":
/*!*******************************************!*\
  !*** ./src/components/OptimisticLink.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OptimisticButton: () => (/* binding */ OptimisticButton),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useOptimisticNavigation: () => (/* binding */ useOptimisticNavigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/NavigationContext */ \"(app-pages-browser)/./src/contexts/NavigationContext.tsx\");\n/* harmony import */ var _utils_cacheStrategy__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/cacheStrategy */ \"(app-pages-browser)/./src/utils/cacheStrategy.ts\");\n/* __next_internal_client_entry_do_not_use__ default,OptimisticButton,useOptimisticNavigation auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\nconst OptimisticLink = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(_c = _s((param, ref)=>{\n    let { href, children, className = '', prefetch = true, onClick, onMouseEnter, ...props } = param;\n    _s();\n    const { navigateOptimistically, isNavigating, targetRoute } = (0,_contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_3__.useNavigation)();\n    const handleClick = (e)=>{\n        // Don't prevent default for external links or special cases\n        if (href.startsWith('http') || href.startsWith('mailto:') || href.startsWith('tel:')) {\n            onClick === null || onClick === void 0 ? void 0 : onClick(e);\n            return;\n        }\n        // Don't prevent default if user is holding modifier keys\n        if (e.metaKey || e.ctrlKey || e.shiftKey || e.altKey) {\n            onClick === null || onClick === void 0 ? void 0 : onClick(e);\n            return;\n        }\n        // Prevent default navigation and use optimistic loading\n        e.preventDefault();\n        // Call custom onClick if provided\n        onClick === null || onClick === void 0 ? void 0 : onClick(e);\n        // Start optimistic navigation\n        navigateOptimistically(href);\n    };\n    const handleMouseEnter = (e)=>{\n        // Prefetch on hover for better performance\n        if (prefetch && href.startsWith('/')) {\n            _utils_cacheStrategy__WEBPACK_IMPORTED_MODULE_4__.prefetcher.schedulePrefetch(href);\n        }\n        // Call custom onMouseEnter if provided\n        onMouseEnter === null || onMouseEnter === void 0 ? void 0 : onMouseEnter(e);\n    };\n    // Add loading state to className if this link is being navigated to\n    const isCurrentlyNavigating = isNavigating && targetRoute === href;\n    const finalClassName = \"\".concat(className, \" \").concat(isCurrentlyNavigating ? 'optimistic-loading' : '').trim();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n        ref: ref,\n        href: href,\n        className: finalClassName,\n        onClick: handleClick,\n        onMouseEnter: handleMouseEnter,\n        prefetch: prefetch,\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticLink.tsx\",\n        lineNumber: 76,\n        columnNumber: 7\n    }, undefined);\n}, \"6iadbAj6+23p8LPCxYTKXwxcagM=\", false, function() {\n    return [\n        _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_3__.useNavigation\n    ];\n})), \"6iadbAj6+23p8LPCxYTKXwxcagM=\", false, function() {\n    return [\n        _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_3__.useNavigation\n    ];\n});\n_c1 = OptimisticLink;\nOptimisticLink.displayName = 'OptimisticLink';\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (OptimisticLink);\nconst OptimisticButton = /*#__PURE__*/ _s1((0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(_c2 = _s1((param, ref)=>{\n    let { href, children, className = '', onClick, onMouseEnter, disabled = false, type = 'button', ...props } = param;\n    _s1();\n    const { navigateOptimistically, isNavigating, targetRoute } = (0,_contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_3__.useNavigation)();\n    const handleClick = (e)=>{\n        if (disabled) return;\n        // Call custom onClick first\n        onClick === null || onClick === void 0 ? void 0 : onClick(e);\n        // If href is provided, navigate optimistically\n        if (href && href.startsWith('/')) {\n            navigateOptimistically(href);\n        }\n    };\n    const handleMouseEnter = (e)=>{\n        // Prefetch on hover if href is provided\n        if (href && href.startsWith('/')) {\n            _utils_cacheStrategy__WEBPACK_IMPORTED_MODULE_4__.prefetcher.schedulePrefetch(href);\n        }\n        // Call custom onMouseEnter if provided\n        onMouseEnter === null || onMouseEnter === void 0 ? void 0 : onMouseEnter(e);\n    };\n    // Add loading state to className if this button's href is being navigated to\n    const isCurrentlyNavigating = isNavigating && targetRoute === href;\n    const finalClassName = \"\".concat(className, \" \").concat(isCurrentlyNavigating ? 'optimistic-loading' : '', \" \").concat(disabled ? 'opacity-50 cursor-not-allowed' : '').trim();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        ref: ref,\n        type: type,\n        className: finalClassName,\n        onClick: handleClick,\n        onMouseEnter: handleMouseEnter,\n        disabled: disabled,\n        ...props,\n        children: [\n            children,\n            isCurrentlyNavigating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"ml-2 inline-block w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticLink.tsx\",\n                lineNumber: 161,\n                columnNumber: 11\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticLink.tsx\",\n        lineNumber: 150,\n        columnNumber: 7\n    }, undefined);\n}, \"6iadbAj6+23p8LPCxYTKXwxcagM=\", false, function() {\n    return [\n        _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_3__.useNavigation\n    ];\n})), \"6iadbAj6+23p8LPCxYTKXwxcagM=\", false, function() {\n    return [\n        _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_3__.useNavigation\n    ];\n});\n_c3 = OptimisticButton;\nOptimisticButton.displayName = 'OptimisticButton';\n// Hook for programmatic optimistic navigation\nfunction useOptimisticNavigation() {\n    _s2();\n    const { navigateOptimistically, isNavigating, targetRoute, isPageCached } = (0,_contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_3__.useNavigation)();\n    const navigate = (href)=>{\n        if (href.startsWith('/')) {\n            navigateOptimistically(href);\n        } else {\n            // For external links, use regular navigation\n            window.location.href = href;\n        }\n    };\n    const isNavigatingTo = (href)=>{\n        return isNavigating && targetRoute === href;\n    };\n    const isCached = (href)=>{\n        return isPageCached(href);\n    };\n    return {\n        navigate,\n        isNavigating,\n        targetRoute,\n        isNavigatingTo,\n        isCached\n    };\n}\n_s2(useOptimisticNavigation, \"L8/PZCoRzd5hsXKrUXO+HIANQpk=\", false, function() {\n    return [\n        _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_3__.useNavigation\n    ];\n});\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"OptimisticLink$forwardRef\");\n$RefreshReg$(_c1, \"OptimisticLink\");\n$RefreshReg$(_c2, \"OptimisticButton$forwardRef\");\n$RefreshReg$(_c3, \"OptimisticButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/OptimisticLink.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/landing/LandingNavbar.tsx":
/*!**************************************************!*\
  !*** ./src/components/landing/LandingNavbar.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LandingNavbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_OptimisticLink__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/OptimisticLink */ \"(app-pages-browser)/./src/components/OptimisticLink.tsx\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction LandingNavbar() {\n    _s();\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"fixed top-0 left-0 right-0 z-50 bg-black/90 backdrop-blur-md border-b border-gray-800\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\",\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-white rounded-lg flex items-center justify-center p-0.5\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            src: \"/roukey_logo.png\",\n                                            alt: \"RouKey\",\n                                            width: 28,\n                                            height: 28,\n                                            className: \"object-cover\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                            lineNumber: 22,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                        lineNumber: 21,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xl font-bold text-white\",\n                                        children: \"RouKey\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                        lineNumber: 30,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                lineNumber: 20,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                            lineNumber: 19,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_OptimisticLink__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    href: \"/features\",\n                                    className: \"text-gray-300 hover:text-white transition-colors\",\n                                    children: \"Features\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                    lineNumber: 36,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_OptimisticLink__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    href: \"/pricing\",\n                                    className: \"text-gray-300 hover:text-white transition-colors\",\n                                    children: \"Pricing\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_OptimisticLink__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    href: \"/about\",\n                                    className: \"text-gray-300 hover:text-white transition-colors\",\n                                    children: \"About\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_OptimisticLink__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    href: \"/auth/signin\",\n                                    className: \"text-gray-300 hover:text-white transition-colors\",\n                                    children: \"Sign In\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_OptimisticLink__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    href: \"/auth/signup\",\n                                    className: \"bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white px-6 py-2 rounded-lg hover:shadow-lg hover:shadow-orange-500/25 transition-all duration-300 font-semibold\",\n                                    children: \"Get Started\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                                className: \"text-gray-300 hover:text-white transition-colors\",\n                                children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, this),\n                isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: -10\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    exit: {\n                        opacity: 0,\n                        y: -10\n                    },\n                    className: \"md:hidden py-4 border-t border-gray-800\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/features\",\n                                className: \"text-gray-300 hover:text-white transition-colors\",\n                                children: \"Features\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/pricing\",\n                                className: \"text-gray-300 hover:text-white transition-colors\",\n                                children: \"Pricing\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/about\",\n                                className: \"text-gray-300 hover:text-white transition-colors\",\n                                children: \"About\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/auth/signin\",\n                                className: \"text-gray-300 hover:text-white transition-colors\",\n                                children: \"Sign In\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/auth/signup\",\n                                className: \"bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white px-4 py-2 rounded-lg hover:shadow-lg hover:shadow-orange-500/25 transition-all duration-300 text-center font-semibold\",\n                                children: \"Get Started\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n_s(LandingNavbar, \"vK10R+uCyHfZ4DZVnxbYkMWJB8g=\");\n_c = LandingNavbar;\nvar _c;\n$RefreshReg$(_c, \"LandingNavbar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/LandingNavbar.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/NavigationContext.tsx":
/*!********************************************!*\
  !*** ./src/contexts/NavigationContext.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NavigationProvider: () => (/* binding */ NavigationProvider),\n/* harmony export */   useNavigation: () => (/* binding */ useNavigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ NavigationProvider,useNavigation auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst NavigationContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction NavigationProvider(param) {\n    let { children } = param;\n    _s();\n    const [isNavigating, setIsNavigating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [targetRoute, setTargetRoute] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [navigationHistory, setNavigationHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [cachedPages, setCachedPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const timeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const navigationQueueRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const currentNavigationIdRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const lastNavigationTimeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    const clickCountRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({});\n    const clickTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({});\n    // Handle client-side hydration\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NavigationProvider.useEffect\": ()=>{\n            setIsClient(true);\n        }\n    }[\"NavigationProvider.useEffect\"], []);\n    // Safe console logging that only runs on client\n    const safeLog = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NavigationProvider.useCallback[safeLog]\": (message)=>{\n            if (isClient && \"object\" !== 'undefined') {\n                console.log(message);\n            }\n        }\n    }[\"NavigationProvider.useCallback[safeLog]\"], [\n        isClient\n    ]);\n    // Track navigation history and cache pages\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NavigationProvider.useEffect\": ()=>{\n            if (pathname && !navigationHistory.includes(pathname)) {\n                setNavigationHistory({\n                    \"NavigationProvider.useEffect\": (prev)=>[\n                            ...prev,\n                            pathname\n                        ]\n                }[\"NavigationProvider.useEffect\"]);\n                setCachedPages({\n                    \"NavigationProvider.useEffect\": (prev)=>new Set([\n                            ...prev,\n                            pathname\n                        ])\n                }[\"NavigationProvider.useEffect\"]);\n            }\n        }\n    }[\"NavigationProvider.useEffect\"], [\n        pathname,\n        navigationHistory\n    ]);\n    // Clear navigation state when route actually changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NavigationProvider.useEffect\": ()=>{\n            safeLog(\"\\uD83D\\uDD0D [OPTIMISTIC NAV] Route check: target=\".concat(targetRoute, \", current=\").concat(pathname, \", navigationId=\").concat(currentNavigationIdRef.current));\n            if (targetRoute && currentNavigationIdRef.current) {\n                // Exact route matching only - be more precise\n                const isRouteMatch = pathname === targetRoute;\n                if (isRouteMatch) {\n                    safeLog(\"✅ [OPTIMISTIC NAV] Navigation completed: \".concat(targetRoute, \" -> \").concat(pathname));\n                    // Clear timeout if navigation completed successfully\n                    if (timeoutRef.current) {\n                        clearTimeout(timeoutRef.current);\n                        timeoutRef.current = null;\n                    }\n                    // Clear navigation state immediately\n                    setIsNavigating(false);\n                    setTargetRoute(null);\n                    currentNavigationIdRef.current = null;\n                    // Clear any remaining queued navigations for this route\n                    navigationQueueRef.current = navigationQueueRef.current.filter({\n                        \"NavigationProvider.useEffect\": (item)=>item.route !== targetRoute\n                    }[\"NavigationProvider.useEffect\"]);\n                }\n            }\n        }\n    }[\"NavigationProvider.useEffect\"], [\n        pathname,\n        targetRoute,\n        safeLog\n    ]);\n    // Additional immediate route change detection\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NavigationProvider.useEffect\": ()=>{\n            // If we're navigating but the pathname has already changed to match target\n            if (isNavigating && targetRoute && pathname === targetRoute) {\n                safeLog(\"\\uD83D\\uDE80 [OPTIMISTIC NAV] Immediate route match detected, clearing navigation state\");\n                setIsNavigating(false);\n                setTargetRoute(null);\n                if (timeoutRef.current) {\n                    clearTimeout(timeoutRef.current);\n                    timeoutRef.current = null;\n                }\n            }\n        }\n    }[\"NavigationProvider.useEffect\"], [\n        pathname,\n        targetRoute,\n        isNavigating,\n        safeLog\n    ]);\n    // Check if page is cached (visited before)\n    const isPageCached = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NavigationProvider.useCallback[isPageCached]\": (route)=>{\n            return cachedPages.has(route);\n        }\n    }[\"NavigationProvider.useCallback[isPageCached]\"], [\n        cachedPages\n    ]);\n    // Process navigation queue\n    const processNavigationQueue = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NavigationProvider.useCallback[processNavigationQueue]\": ()=>{\n            if (navigationQueueRef.current.length === 0) return;\n            // Get the most recent navigation request\n            const latestNavigation = navigationQueueRef.current[navigationQueueRef.current.length - 1];\n            // Clear all older navigation requests\n            navigationQueueRef.current = [\n                latestNavigation\n            ];\n            const { route, id } = latestNavigation;\n            safeLog(\"\\uD83D\\uDE80 [OPTIMISTIC NAV] Processing navigation to: \".concat(route, \" (id: \").concat(id, \")\"));\n            // Clear any existing timeout\n            if (timeoutRef.current) {\n                clearTimeout(timeoutRef.current);\n                timeoutRef.current = null;\n            }\n            // Update navigation ID and confirm target route\n            currentNavigationIdRef.current = id;\n            // Target route should already be set by navigateOptimistically\n            // For cached pages, show minimal loading\n            const isCached = isPageCached(route);\n            if (isCached) {\n                safeLog(\"⚡ [OPTIMISTIC NAV] Using cached navigation for: \".concat(route));\n                // Clear quickly for cached pages\n                setTimeout({\n                    \"NavigationProvider.useCallback[processNavigationQueue]\": ()=>{\n                        if (currentNavigationIdRef.current === id) {\n                            setIsNavigating(false);\n                        }\n                    }\n                }[\"NavigationProvider.useCallback[processNavigationQueue]\"], 100);\n            }\n            // Start actual navigation immediately\n            try {\n                router.push(route);\n            } catch (error) {\n                safeLog(\"❌ [OPTIMISTIC NAV] Router.push failed for: \".concat(route, \", using fallback\"));\n                // Fallback to window.location if router fails\n                window.location.href = route;\n                return;\n            }\n            // Set timeout for fallback (shorter for cached pages, but more generous)\n            const timeoutDuration = isCached ? 800 : 3000;\n            timeoutRef.current = setTimeout({\n                \"NavigationProvider.useCallback[processNavigationQueue]\": ()=>{\n                    safeLog(\"⚠️ [OPTIMISTIC NAV] Timeout reached for: \".concat(route, \" (id: \").concat(id, \"), current path: \").concat(pathname));\n                    if (currentNavigationIdRef.current === id) {\n                        // Try fallback navigation\n                        safeLog(\"\\uD83D\\uDD04 [OPTIMISTIC NAV] Attempting fallback navigation to: \".concat(route));\n                        try {\n                            window.location.href = route;\n                        } catch (fallbackError) {\n                            safeLog(\"❌ [OPTIMISTIC NAV] Fallback navigation failed: \".concat(fallbackError));\n                        }\n                        setIsNavigating(false);\n                        setTargetRoute(null);\n                        currentNavigationIdRef.current = null;\n                    }\n                    timeoutRef.current = null;\n                }\n            }[\"NavigationProvider.useCallback[processNavigationQueue]\"], timeoutDuration);\n        }\n    }[\"NavigationProvider.useCallback[processNavigationQueue]\"], [\n        router,\n        pathname,\n        isPageCached,\n        safeLog\n    ]);\n    const navigateOptimistically = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NavigationProvider.useCallback[navigateOptimistically]\": (href)=>{\n            // Don't navigate if we're already on this exact route\n            if (pathname === href || !isClient) {\n                return;\n            }\n            const now = Date.now();\n            // Simple debounce to prevent rapid clicks\n            if (now - lastNavigationTimeRef.current < 100 && targetRoute === href) {\n                safeLog(\"\\uD83D\\uDD04 [OPTIMISTIC NAV] Debouncing duplicate navigation to: \".concat(href));\n                return;\n            }\n            lastNavigationTimeRef.current = now;\n            // Track click count for escape hatch\n            if (!clickCountRef.current[href]) {\n                clickCountRef.current[href] = 0;\n            }\n            clickCountRef.current[href]++;\n            // Clear click count after 2 seconds\n            if (clickTimeoutRef.current[href]) {\n                clearTimeout(clickTimeoutRef.current[href]);\n            }\n            clickTimeoutRef.current[href] = setTimeout({\n                \"NavigationProvider.useCallback[navigateOptimistically]\": ()=>{\n                    clickCountRef.current[href] = 0;\n                }\n            }[\"NavigationProvider.useCallback[navigateOptimistically]\"], 2000);\n            // If user clicks same route 3+ times quickly, force regular navigation\n            if (clickCountRef.current[href] >= 3) {\n                safeLog(\"\\uD83D\\uDEA8 [OPTIMISTIC NAV] Force navigation escape hatch for: \".concat(href));\n                clickCountRef.current[href] = 0;\n                if (true) {\n                    window.location.href = href;\n                }\n                return;\n            }\n            // Clear any existing navigation state first\n            if (timeoutRef.current) {\n                clearTimeout(timeoutRef.current);\n                timeoutRef.current = null;\n            }\n            // IMMEDIATE visual feedback - set loading state right away\n            setIsNavigating(true);\n            setTargetRoute(href);\n            // Generate unique ID for this navigation\n            const navigationId = \"nav_\".concat(now, \"_\").concat(Math.random().toString(36).substr(2, 9));\n            // Clear queue and add new navigation\n            navigationQueueRef.current = [\n                {\n                    route: href,\n                    timestamp: now,\n                    id: navigationId\n                }\n            ];\n            // Process queue immediately for instant feedback\n            processNavigationQueue();\n        }\n    }[\"NavigationProvider.useCallback[navigateOptimistically]\"], [\n        pathname,\n        targetRoute,\n        processNavigationQueue,\n        safeLog,\n        isClient\n    ]);\n    const clearNavigation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NavigationProvider.useCallback[clearNavigation]\": ()=>{\n            if (timeoutRef.current) {\n                clearTimeout(timeoutRef.current);\n                timeoutRef.current = null;\n            }\n            setIsNavigating(false);\n            setTargetRoute(null);\n            currentNavigationIdRef.current = null;\n            navigationQueueRef.current = [];\n        }\n    }[\"NavigationProvider.useCallback[clearNavigation]\"], []);\n    // Additional safety: clear navigation state when document becomes visible\n    // This handles cases where the route change detection might miss\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NavigationProvider.useEffect\": ()=>{\n            // Only add event listener on client side\n            if (!isClient || \"object\" === 'undefined') return;\n            const handleVisibilityChange = {\n                \"NavigationProvider.useEffect.handleVisibilityChange\": ()=>{\n                    if (!document.hidden && isNavigating) {\n                        safeLog(\"\\uD83D\\uDC41️ [OPTIMISTIC NAV] Document visible, checking if navigation should clear\");\n                        // Small delay to allow route to update\n                        setTimeout({\n                            \"NavigationProvider.useEffect.handleVisibilityChange\": ()=>{\n                                if (targetRoute) {\n                                    const isRouteMatch = pathname === targetRoute;\n                                    if (isRouteMatch) {\n                                        safeLog(\"\\uD83D\\uDD27 [OPTIMISTIC NAV] Force clearing navigation state\");\n                                        setIsNavigating(false);\n                                        setTargetRoute(null);\n                                        if (timeoutRef.current) {\n                                            clearTimeout(timeoutRef.current);\n                                            timeoutRef.current = null;\n                                        }\n                                    }\n                                }\n                            }\n                        }[\"NavigationProvider.useEffect.handleVisibilityChange\"], 100);\n                    }\n                }\n            }[\"NavigationProvider.useEffect.handleVisibilityChange\"];\n            document.addEventListener('visibilitychange', handleVisibilityChange);\n            return ({\n                \"NavigationProvider.useEffect\": ()=>document.removeEventListener('visibilitychange', handleVisibilityChange)\n            })[\"NavigationProvider.useEffect\"];\n        }\n    }[\"NavigationProvider.useEffect\"], [\n        isNavigating,\n        targetRoute,\n        pathname,\n        safeLog,\n        isClient\n    ]);\n    // Cleanup timeout on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NavigationProvider.useEffect\": ()=>{\n            return ({\n                \"NavigationProvider.useEffect\": ()=>{\n                    if (timeoutRef.current) {\n                        clearTimeout(timeoutRef.current);\n                    }\n                }\n            })[\"NavigationProvider.useEffect\"];\n        }\n    }[\"NavigationProvider.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavigationContext.Provider, {\n        value: {\n            isNavigating,\n            targetRoute,\n            navigateOptimistically,\n            clearNavigation,\n            isPageCached,\n            navigationHistory\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\contexts\\\\NavigationContext.tsx\",\n        lineNumber: 290,\n        columnNumber: 5\n    }, this);\n}\n_s(NavigationProvider, \"vATWKAiHgWda+aWoMsrxP4ttJDk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = NavigationProvider;\nfunction useNavigation() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(NavigationContext);\n    if (context === undefined) {\n        throw new Error('useNavigation must be used within a NavigationProvider');\n    }\n    return context;\n}\n_s1(useNavigation, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"NavigationProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/NavigationContext.tsx\n"));

/***/ })

});