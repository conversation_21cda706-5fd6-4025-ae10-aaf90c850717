'use client';

import { forwardRef, MouseEvent, ReactNode } from 'react';
import Link from 'next/link';
import { useNavigation } from '@/contexts/NavigationContext';
import { prefetcher } from '@/utils/cacheStrategy';

interface OptimisticLinkProps {
  href: string;
  children: ReactNode;
  className?: string;
  prefetch?: boolean;
  replace?: boolean;
  scroll?: boolean;
  shallow?: boolean;
  onClick?: (e: MouseEvent<HTMLAnchorElement>) => void;
  onMouseEnter?: (e: MouseEvent<HTMLAnchorElement>) => void;
  target?: string;
  rel?: string;
  'aria-label'?: string;
  id?: string;
  role?: string;
  tabIndex?: number;
}

const OptimisticLink = forwardRef<HTMLAnchorElement, OptimisticLinkProps>(
  ({ 
    href, 
    children, 
    className = '', 
    prefetch = true,
    onClick,
    onMouseEnter,
    ...props 
  }, ref) => {
    const { navigateOptimistically, isNavigating, targetRoute } = useNavigation();

    const handleClick = (e: MouseEvent<HTMLAnchorElement>) => {
      // Don't prevent default for external links or special cases
      if (href.startsWith('http') || href.startsWith('mailto:') || href.startsWith('tel:')) {
        onClick?.(e);
        return;
      }

      // Don't prevent default if user is holding modifier keys
      if (e.metaKey || e.ctrlKey || e.shiftKey || e.altKey) {
        onClick?.(e);
        return;
      }

      // Prevent default navigation and use optimistic loading
      e.preventDefault();
      
      // Call custom onClick if provided
      onClick?.(e);
      
      // Start optimistic navigation
      navigateOptimistically(href);
    };

    const handleMouseEnter = (e: MouseEvent<HTMLAnchorElement>) => {
      // Prefetch on hover for better performance
      if (prefetch && href.startsWith('/')) {
        prefetcher.schedulePrefetch(href);
      }
      
      // Call custom onMouseEnter if provided
      onMouseEnter?.(e);
    };

    // Add loading state to className if this link is being navigated to
    const isCurrentlyNavigating = isNavigating && targetRoute === href;
    const finalClassName = `${className} ${isCurrentlyNavigating ? 'optimistic-loading' : ''}`.trim();

    return (
      <Link
        ref={ref}
        href={href}
        className={finalClassName}
        onClick={handleClick}
        onMouseEnter={handleMouseEnter}
        prefetch={prefetch}
        {...props}
      >
        {children}
      </Link>
    );
  }
);

OptimisticLink.displayName = 'OptimisticLink';

export default OptimisticLink;

// Button component with optimistic navigation
interface OptimisticButtonProps {
  href?: string;
  children: ReactNode;
  className?: string;
  onClick?: (e: MouseEvent<HTMLButtonElement>) => void;
  onMouseEnter?: (e: MouseEvent<HTMLButtonElement>) => void;
  disabled?: boolean;
  type?: 'button' | 'submit' | 'reset';
  'aria-label'?: string;
  id?: string;
  role?: string;
  tabIndex?: number;
}

export const OptimisticButton = forwardRef<HTMLButtonElement, OptimisticButtonProps>(
  ({ 
    href, 
    children, 
    className = '', 
    onClick,
    onMouseEnter,
    disabled = false,
    type = 'button',
    ...props 
  }, ref) => {
    const { navigateOptimistically, isNavigating, targetRoute } = useNavigation();

    const handleClick = (e: MouseEvent<HTMLButtonElement>) => {
      if (disabled) return;
      
      // Call custom onClick first
      onClick?.(e);
      
      // If href is provided, navigate optimistically
      if (href && href.startsWith('/')) {
        navigateOptimistically(href);
      }
    };

    const handleMouseEnter = (e: MouseEvent<HTMLButtonElement>) => {
      // Prefetch on hover if href is provided
      if (href && href.startsWith('/')) {
        prefetcher.schedulePrefetch(href);
      }
      
      // Call custom onMouseEnter if provided
      onMouseEnter?.(e);
    };

    // Add loading state to className if this button's href is being navigated to
    const isCurrentlyNavigating = isNavigating && targetRoute === href;
    const finalClassName = `${className} ${isCurrentlyNavigating ? 'optimistic-loading' : ''} ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`.trim();

    return (
      <button
        ref={ref}
        type={type}
        className={finalClassName}
        onClick={handleClick}
        onMouseEnter={handleMouseEnter}
        disabled={disabled}
        {...props}
      >
        {children}
        {isCurrentlyNavigating && (
          <span className="ml-2 inline-block w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
        )}
      </button>
    );
  }
);

OptimisticButton.displayName = 'OptimisticButton';

// Hook for programmatic optimistic navigation
export function useOptimisticNavigation() {
  const { navigateOptimistically, isNavigating, targetRoute, isPageCached } = useNavigation();

  const navigate = (href: string) => {
    if (href.startsWith('/')) {
      navigateOptimistically(href);
    } else {
      // For external links, use regular navigation
      window.location.href = href;
    }
  };

  const isNavigatingTo = (href: string) => {
    return isNavigating && targetRoute === href;
  };

  const isCached = (href: string) => {
    return isPageCached(href);
  };

  return {
    navigate,
    isNavigating,
    targetRoute,
    isNavigatingTo,
    isCached
  };
}
