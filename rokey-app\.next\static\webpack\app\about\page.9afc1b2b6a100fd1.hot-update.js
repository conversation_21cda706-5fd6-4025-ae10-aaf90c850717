"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/about/page",{

/***/ "(app-pages-browser)/./src/components/OptimisticLink.tsx":
/*!*******************************************!*\
  !*** ./src/components/OptimisticLink.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OptimisticButton: () => (/* binding */ OptimisticButton),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useOptimisticNavigation: () => (/* binding */ useOptimisticNavigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _utils_cacheStrategy__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/cacheStrategy */ \"(app-pages-browser)/./src/utils/cacheStrategy.ts\");\n/* __next_internal_client_entry_do_not_use__ default,OptimisticButton,useOptimisticNavigation auto */ \n\n\n\nconst OptimisticLink = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(_c = (param, ref)=>{\n    let { href, children, className = '', prefetch = true, onClick, onMouseEnter, ...props } = param;\n    // Use try-catch to handle cases where NavigationProvider might not be available\n    let navigationContext;\n    try {\n        navigationContext = useNavigation();\n    } catch (error) {\n        // Fallback to regular Link behavior if NavigationProvider is not available\n        console.warn('NavigationProvider not found, falling back to regular Link behavior');\n        navigationContext = null;\n    }\n    const { navigateOptimistically, isNavigating, targetRoute } = navigationContext || {\n        navigateOptimistically: ()=>{},\n        isNavigating: false,\n        targetRoute: null\n    };\n    const handleClick = (e)=>{\n        // Don't prevent default for external links or special cases\n        if (href.startsWith('http') || href.startsWith('mailto:') || href.startsWith('tel:')) {\n            onClick === null || onClick === void 0 ? void 0 : onClick(e);\n            return;\n        }\n        // Don't prevent default if user is holding modifier keys\n        if (e.metaKey || e.ctrlKey || e.shiftKey || e.altKey) {\n            onClick === null || onClick === void 0 ? void 0 : onClick(e);\n            return;\n        }\n        // Prevent default navigation and use optimistic loading\n        e.preventDefault();\n        // Call custom onClick if provided\n        onClick === null || onClick === void 0 ? void 0 : onClick(e);\n        // Start optimistic navigation\n        navigateOptimistically(href);\n    };\n    const handleMouseEnter = (e)=>{\n        // Prefetch on hover for better performance\n        if (prefetch && href.startsWith('/')) {\n            _utils_cacheStrategy__WEBPACK_IMPORTED_MODULE_3__.prefetcher.schedulePrefetch(href);\n        }\n        // Call custom onMouseEnter if provided\n        onMouseEnter === null || onMouseEnter === void 0 ? void 0 : onMouseEnter(e);\n    };\n    // Add loading state to className if this link is being navigated to\n    const isCurrentlyNavigating = isNavigating && targetRoute === href;\n    const finalClassName = \"\".concat(className, \" \").concat(isCurrentlyNavigating ? 'optimistic-loading' : '').trim();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n        ref: ref,\n        href: href,\n        className: finalClassName,\n        onClick: handleClick,\n        onMouseEnter: handleMouseEnter,\n        prefetch: prefetch,\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticLink.tsx\",\n        lineNumber: 90,\n        columnNumber: 7\n    }, undefined);\n});\n_c1 = OptimisticLink;\nOptimisticLink.displayName = 'OptimisticLink';\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (OptimisticLink);\nconst OptimisticButton = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(_c2 = (param, ref)=>{\n    let { href, children, className = '', onClick, onMouseEnter, disabled = false, type = 'button', ...props } = param;\n    // Use try-catch to handle cases where NavigationProvider might not be available\n    let navigationContext;\n    try {\n        navigationContext = useNavigation();\n    } catch (error) {\n        // Fallback to regular button behavior if NavigationProvider is not available\n        console.warn('NavigationProvider not found, falling back to regular button behavior');\n        navigationContext = null;\n    }\n    const { navigateOptimistically, isNavigating, targetRoute } = navigationContext || {\n        navigateOptimistically: ()=>{},\n        isNavigating: false,\n        targetRoute: null\n    };\n    const handleClick = (e)=>{\n        if (disabled) return;\n        // Call custom onClick first\n        onClick === null || onClick === void 0 ? void 0 : onClick(e);\n        // If href is provided, navigate optimistically\n        if (href && href.startsWith('/')) {\n            navigateOptimistically(href);\n        }\n    };\n    const handleMouseEnter = (e)=>{\n        // Prefetch on hover if href is provided\n        if (href && href.startsWith('/')) {\n            _utils_cacheStrategy__WEBPACK_IMPORTED_MODULE_3__.prefetcher.schedulePrefetch(href);\n        }\n        // Call custom onMouseEnter if provided\n        onMouseEnter === null || onMouseEnter === void 0 ? void 0 : onMouseEnter(e);\n    };\n    // Add loading state to className if this button's href is being navigated to\n    const isCurrentlyNavigating = isNavigating && targetRoute === href;\n    const finalClassName = \"\".concat(className, \" \").concat(isCurrentlyNavigating ? 'optimistic-loading' : '', \" \").concat(disabled ? 'opacity-50 cursor-not-allowed' : '').trim();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        ref: ref,\n        type: type,\n        className: finalClassName,\n        onClick: handleClick,\n        onMouseEnter: handleMouseEnter,\n        disabled: disabled,\n        ...props,\n        children: [\n            children,\n            isCurrentlyNavigating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"ml-2 inline-block w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticLink.tsx\",\n                lineNumber: 189,\n                columnNumber: 11\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticLink.tsx\",\n        lineNumber: 178,\n        columnNumber: 7\n    }, undefined);\n});\n_c3 = OptimisticButton;\nOptimisticButton.displayName = 'OptimisticButton';\n// Hook for programmatic optimistic navigation\nfunction useOptimisticNavigation() {\n    // Use try-catch to handle cases where NavigationProvider might not be available\n    let navigationContext;\n    try {\n        navigationContext = useNavigation();\n    } catch (error) {\n        // Fallback behavior if NavigationProvider is not available\n        console.warn('NavigationProvider not found, falling back to regular navigation behavior');\n        navigationContext = null;\n    }\n    const { navigateOptimistically, isNavigating, targetRoute, isPageCached } = navigationContext || {\n        navigateOptimistically: ()=>{},\n        isNavigating: false,\n        targetRoute: null,\n        isPageCached: ()=>false\n    };\n    const navigate = (href)=>{\n        if (href.startsWith('/')) {\n            navigateOptimistically(href);\n        } else {\n            // For external links, use regular navigation\n            window.location.href = href;\n        }\n    };\n    const isNavigatingTo = (href)=>{\n        return isNavigating && targetRoute === href;\n    };\n    const isCached = (href)=>{\n        return isPageCached(href);\n    };\n    return {\n        navigate,\n        isNavigating,\n        targetRoute,\n        isNavigatingTo,\n        isCached\n    };\n}\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"OptimisticLink$forwardRef\");\n$RefreshReg$(_c1, \"OptimisticLink\");\n$RefreshReg$(_c2, \"OptimisticButton$forwardRef\");\n$RefreshReg$(_c3, \"OptimisticButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/OptimisticLink.tsx\n"));

/***/ })

});