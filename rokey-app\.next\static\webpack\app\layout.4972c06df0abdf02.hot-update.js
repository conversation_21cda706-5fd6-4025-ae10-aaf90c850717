"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"96fd2ef73ede\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcUm9LZXkgQXBwXFxyb2tleS1hcHBcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjk2ZmQyZWY3M2VkZVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/OptimisticPageLoader.tsx":
/*!*************************************************!*\
  !*** ./src/components/OptimisticPageLoader.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ OptimisticPageLoader)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/NavigationContext */ \"(app-pages-browser)/./src/contexts/NavigationContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n// Page skeleton components for instant loading\nconst DashboardSkeleton = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6 animate-pulse\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-8 bg-gray-200 rounded w-1/3\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                lineNumber: 10,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                children: [\n                    1,\n                    2,\n                    3\n                ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white p-6 rounded-lg border\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-4 bg-gray-200 rounded w-1/2 mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                                lineNumber: 14,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-8 bg-gray-200 rounded w-1/4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                                lineNumber: 15,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, i, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                lineNumber: 11,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-6 rounded-lg border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-6 bg-gray-200 rounded w-1/4 mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            1,\n                            2,\n                            3,\n                            4\n                        ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-4 bg-gray-200 rounded\"\n                            }, i, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                                lineNumber: 23,\n                                columnNumber: 11\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                lineNumber: 19,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined);\n_c = DashboardSkeleton;\nconst PricingSkeleton = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8 animate-pulse\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-12 bg-gray-200 rounded w-1/2 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-6 bg-gray-200 rounded w-3/4 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                lineNumber: 32,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                children: [\n                    1,\n                    2,\n                    3\n                ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white p-8 rounded-2xl border-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-6 bg-gray-200 rounded w-1/2 mx-auto mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                                        lineNumber: 40,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-gray-200 rounded w-3/4 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-10 bg-gray-200 rounded w-1/3 mx-auto\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                                        lineNumber: 42,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3 mb-8\",\n                                children: [\n                                    1,\n                                    2,\n                                    3,\n                                    4,\n                                    5\n                                ].map((j)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-gray-200 rounded\"\n                                    }, j, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                                lineNumber: 44,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-12 bg-gray-200 rounded\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, i, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                lineNumber: 36,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n        lineNumber: 31,\n        columnNumber: 3\n    }, undefined);\n_c1 = PricingSkeleton;\nconst FeaturesSkeleton = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8 animate-pulse\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-20 bg-gradient-to-br from-slate-50 to-blue-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-16 bg-gray-200 rounded w-2/3 mx-auto mb-6\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-6 bg-gray-200 rounded w-3/4 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                lineNumber: 58,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-12\",\n                    children: [\n                        1,\n                        2,\n                        3,\n                        4,\n                        5,\n                        6\n                    ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-2xl p-8 border\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-gray-200 rounded-xl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                                        lineNumber: 67,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-6 bg-gray-200 rounded w-1/2 mb-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                                                lineNumber: 69,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-4 bg-gray-200 rounded mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    1,\n                                                    2,\n                                                    3\n                                                ].map((j)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-3 bg-gray-200 rounded w-3/4\"\n                                                    }, j, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                                                        lineNumber: 73,\n                                                        columnNumber: 21\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                                                lineNumber: 71,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 13\n                            }, undefined)\n                        }, i, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                lineNumber: 62,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n        lineNumber: 57,\n        columnNumber: 3\n    }, undefined);\n_c2 = FeaturesSkeleton;\nconst AuthSkeleton = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-blue-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white p-8 rounded-2xl shadow-lg border w-full max-w-md animate-pulse\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-8 bg-gray-200 rounded w-1/2 mx-auto mb-2\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-4 bg-gray-200 rounded w-3/4 mx-auto\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                    lineNumber: 88,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-12 bg-gray-200 rounded\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-12 bg-gray-200 rounded\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-12 bg-gray-200 rounded\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-12 bg-gray-200 rounded\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n            lineNumber: 87,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n        lineNumber: 86,\n        columnNumber: 3\n    }, undefined);\n_c3 = AuthSkeleton;\nconst PlaygroundSkeleton = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6 animate-pulse\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-8 bg-gray-200 rounded w-1/4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                lineNumber: 104,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white p-6 rounded-lg border\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-6 bg-gray-200 rounded w-1/3 mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    1,\n                                    2,\n                                    3\n                                ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-gray-200 rounded\"\n                                    }, i, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 13\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-32 bg-gray-200 rounded mt-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-10 bg-gray-200 rounded mt-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white p-6 rounded-lg border\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-6 bg-gray-200 rounded w-1/3 mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-64 bg-gray-200 rounded\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                lineNumber: 105,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n        lineNumber: 103,\n        columnNumber: 3\n    }, undefined);\n_c4 = PlaygroundSkeleton;\nconst GenericSkeleton = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6 animate-pulse\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-8 bg-gray-200 rounded w-1/3\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                lineNumber: 126,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-4 bg-gray-200 rounded w-2/3\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                lineNumber: 127,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-6 rounded-lg border\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        1,\n                        2,\n                        3,\n                        4,\n                        5\n                    ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-4 bg-gray-200 rounded\"\n                        }, i, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                lineNumber: 128,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n        lineNumber: 125,\n        columnNumber: 3\n    }, undefined);\n_c5 = GenericSkeleton;\nfunction OptimisticPageLoader(param) {\n    let { targetRoute, children } = param;\n    _s();\n    const [showSkeleton, setShowSkeleton] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isContentReady, setIsContentReady] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const timeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    // Use safe navigation hook that returns null instead of throwing\n    const navigationContext = (0,_contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_3__.useNavigationSafe)();\n    const { isPageCached } = navigationContext || {\n        isPageCached: ()=>false\n    };\n    // Determine which skeleton to show based on target route\n    const getSkeletonComponent = (route)=>{\n        if (route.startsWith('/dashboard')) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DashboardSkeleton, {}, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n            lineNumber: 158,\n            columnNumber: 48\n        }, this);\n        if (route.startsWith('/pricing')) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PricingSkeleton, {}, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n            lineNumber: 159,\n            columnNumber: 46\n        }, this);\n        if (route.startsWith('/features')) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeaturesSkeleton, {}, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n            lineNumber: 160,\n            columnNumber: 47\n        }, this);\n        if (route.startsWith('/auth/')) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthSkeleton, {}, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n            lineNumber: 161,\n            columnNumber: 44\n        }, this);\n        if (route.startsWith('/playground')) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlaygroundSkeleton, {}, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n            lineNumber: 162,\n            columnNumber: 49\n        }, this);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GenericSkeleton, {}, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n            lineNumber: 163,\n            columnNumber: 12\n        }, this);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OptimisticPageLoader.useEffect\": ()=>{\n            // If we've reached the target route, start transitioning to real content\n            if (pathname === targetRoute) {\n                const isCached = isPageCached(targetRoute);\n                const delay = isCached ? 50 : 200; // Faster for cached pages\n                timeoutRef.current = setTimeout({\n                    \"OptimisticPageLoader.useEffect\": ()=>{\n                        setIsContentReady(true);\n                        // Hide skeleton after a brief moment to show real content\n                        setTimeout({\n                            \"OptimisticPageLoader.useEffect\": ()=>setShowSkeleton(false)\n                        }[\"OptimisticPageLoader.useEffect\"], 100);\n                    }\n                }[\"OptimisticPageLoader.useEffect\"], delay);\n            }\n            return ({\n                \"OptimisticPageLoader.useEffect\": ()=>{\n                    if (timeoutRef.current) {\n                        clearTimeout(timeoutRef.current);\n                    }\n                }\n            })[\"OptimisticPageLoader.useEffect\"];\n        }\n    }[\"OptimisticPageLoader.useEffect\"], [\n        pathname,\n        targetRoute,\n        isPageCached\n    ]);\n    // Reset state when target route changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OptimisticPageLoader.useEffect\": ()=>{\n            setShowSkeleton(true);\n            setIsContentReady(false);\n        }\n    }[\"OptimisticPageLoader.useEffect\"], [\n        targetRoute\n    ]);\n    // If we're not at the target route yet, show skeleton\n    if (pathname !== targetRoute && showSkeleton) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"optimistic-loading-container\",\n            children: getSkeletonComponent(targetRoute)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n            lineNumber: 195,\n            columnNumber: 7\n        }, this);\n    }\n    // If we're at the target route but content isn't ready, show skeleton with fade\n    if (pathname === targetRoute && showSkeleton && !isContentReady) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"optimistic-loading-container\",\n            children: getSkeletonComponent(targetRoute)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n            lineNumber: 204,\n            columnNumber: 7\n        }, this);\n    }\n    // Show real content with fade-in\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"transition-opacity duration-300 \".concat(isContentReady ? 'opacity-100' : 'opacity-0'),\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n        lineNumber: 212,\n        columnNumber: 5\n    }, this);\n} // Note: useOptimisticNavigation hook is exported from OptimisticLink.tsx to avoid duplication\n_s(OptimisticPageLoader, \"7KcrZzQh8ybGNgdgs0yJRpCEhuo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_3__.useNavigationSafe\n    ];\n});\n_c6 = OptimisticPageLoader;\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"DashboardSkeleton\");\n$RefreshReg$(_c1, \"PricingSkeleton\");\n$RefreshReg$(_c2, \"FeaturesSkeleton\");\n$RefreshReg$(_c3, \"AuthSkeleton\");\n$RefreshReg$(_c4, \"PlaygroundSkeleton\");\n$RefreshReg$(_c5, \"GenericSkeleton\");\n$RefreshReg$(_c6, \"OptimisticPageLoader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/OptimisticPageLoader.tsx\n"));

/***/ })

});