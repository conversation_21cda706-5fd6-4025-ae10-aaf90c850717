'use client';

import Navbar from "@/components/Navbar";
import Sidebar from "@/components/Sidebar";
import { useSidebar } from "@/contexts/SidebarContext";
import { useNavigation } from "@/contexts/NavigationContext";
import OptimisticLoadingScreen from "@/components/OptimisticLoadingScreen";
import OptimisticPageLoader from "@/components/OptimisticPageLoader";
import { useAdvancedPreloading } from "@/hooks/useAdvancedPreloading";

export default function LayoutContent({ children }: { children: React.ReactNode }) {
  const { isCollapsed, collapseSidebar } = useSidebar();
  const { isNavigating, targetRoute, isPageCached } = useNavigation();

  // Initialize advanced preloading system
  useAdvancedPreloading({
    maxConcurrent: 2,
    idleTimeout: 1500,
    backgroundDelay: 3000
  });

  return (
    <div className="flex flex-1 overflow-hidden">
      {/* Desktop Sidebar */}
      <div className="hidden lg:block">
        <Sidebar />
      </div>

      {/* Mobile Sidebar Overlay */}
      <div className={`lg:hidden fixed inset-0 z-50 ${isCollapsed ? 'pointer-events-none' : ''}`}>
        {/* Backdrop */}
        <div
          onClick={collapseSidebar}
          className={`absolute inset-0 bg-black transition-opacity duration-200 ease-out cursor-pointer ${
            isCollapsed ? 'opacity-0' : 'opacity-50'
          }`}
        />

        {/* Sidebar */}
        <div className={`absolute left-0 top-0 h-full transform transition-transform duration-200 ease-out ${
          isCollapsed ? '-translate-x-full' : 'translate-x-0'
        }`}>
          <Sidebar />
        </div>
      </div>

      {/* Main content area */}
      <div className="flex-1 flex flex-col overflow-hidden min-w-0">
        <Navbar />
        <main className="flex-1 overflow-y-auto content-area">
          <div className="p-4 sm:p-6 lg:p-8 max-w-7xl mx-auto w-full">
            <div className="page-transition">
              {isNavigating && targetRoute ? (
                <OptimisticPageLoader targetRoute={targetRoute}>
                  {children}
                </OptimisticPageLoader>
              ) : (
                children
              )}
            </div>
          </div>
        </main>
      </div>


    </div>
  );
}
