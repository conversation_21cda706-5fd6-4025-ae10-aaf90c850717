'use client';

import React, { Suspense, lazy } from 'react';
import { motion } from 'framer-motion';
import {
  BoltIcon,
  ShieldCheckIcon,
  ChartBarIcon,
  CpuChipIcon,
  ArrowPathIcon,
  CloudIcon,
  CodeBracketIcon,
  LightBulbIcon,
  RocketLaunchIcon,
  CogIcon
} from '@heroicons/react/24/outline';
import LandingNavbar from '@/components/landing/LandingNavbar';
import { usePerformanceOptimization } from '@/hooks/usePerformanceOptimization';

// Lazy load footer for better initial load
const Footer = lazy(() => import('@/components/landing/Footer'));

const features = [
  {
    icon: BoltIcon,
    title: "Intelligent Routing",
    description: "Advanced AI algorithms analyze your prompts and automatically route them to the optimal model for best results.",
    details: [
      "Real-time prompt analysis",
      "Context-aware routing decisions",
      "Performance optimization",
      "Cost-effective model selection"
    ]
  },
  {
    icon: ShieldCheckIcon,
    title: "Enterprise Security",
    description: "Bank-grade security with end-to-end encryption, compliance certifications, and audit trails.",
    details: [
      "SOC 2 Type II compliance",
      "End-to-end encryption",
      "Complete audit trails",
      "Role-based access control"
    ]
  },
  {
    icon: ChartBarIcon,
    title: "Advanced Analytics",
    description: "Comprehensive insights into model performance, costs, and usage patterns with real-time dashboards.",
    details: [
      "Real-time performance metrics",
      "Cost optimization insights",
      "Usage pattern analysis",
      "Custom reporting"
    ]
  },
  {
    icon: CpuChipIcon,
    title: "300+ AI Models",
    description: "Access to the largest collection of AI models from leading providers, all through one unified API.",
    details: [
      "OpenAI, Anthropic, Google, Meta",
      "Specialized domain models",
      "Latest model versions",
      "Custom model integration"
    ]
  },
  {
    icon: ArrowPathIcon,
    title: "Auto-Failover",
    description: "Automatic failover and retry mechanisms ensure 99.9% uptime for your AI applications.",
    details: [
      "Intelligent failover routing",
      "Automatic retry logic",
      "Health monitoring",
      "Zero-downtime switching"
    ]
  },
  {
    icon: CloudIcon,
    title: "Global Infrastructure",
    description: "Distributed infrastructure across multiple regions for low latency and high availability.",
    details: [
      "Multi-region deployment",
      "Edge computing optimization",
      "CDN acceleration",
      "99.9% uptime SLA"
    ]
  },
  {
    icon: CodeBracketIcon,
    title: "Developer-First",
    description: "Built by developers, for developers. Simple APIs, comprehensive docs, and powerful SDKs.",
    details: [
      "RESTful API design",
      "Multiple SDK languages",
      "Interactive documentation",
      "Code examples & tutorials"
    ]
  },
  {
    icon: LightBulbIcon,
    title: "Smart Optimization",
    description: "Continuous learning algorithms optimize routing decisions based on your usage patterns.",
    details: [
      "Machine learning optimization",
      "Pattern recognition",
      "Adaptive routing",
      "Performance tuning"
    ]
  },
  {
    icon: RocketLaunchIcon,
    title: "Rapid Deployment",
    description: "Get started in minutes with our simple integration process and migration tools.",
    details: [
      "5-minute setup",
      "Migration assistance",
      "Zero-code integration",
      "Instant activation"
    ]
  },
  {
    icon: CogIcon,
    title: "Custom Configurations",
    description: "Flexible routing rules and custom configurations to match your specific requirements.",
    details: [
      "Custom routing logic",
      "Business rule engine",
      "A/B testing support",
      "Configuration templates"
    ]
  }
];

export default function FeaturesPage() {
  const { startMeasurement, endMeasurement } = usePerformanceOptimization('FeaturesPage', {
    enableMonitoring: true,
    enableMemoryTracking: true,
    warningThresholds: {
      renderTime: 200, // Features page has many animations
      memoryUsage: 40 * 1024 * 1024 // 40MB
    }
  });

  React.useEffect(() => {
    startMeasurement();
    return () => endMeasurement();
  }, [startMeasurement, endMeasurement]);

  return (
    <div className="min-h-screen bg-white">
      <LandingNavbar />

      <main className="pt-20">
        {/* Hero Section */}
        <section className="py-20 bg-gradient-to-br from-slate-50 to-blue-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
                Powerful Features for
                <span className="text-[#ff6b35] block">Modern AI Development</span>
              </h1>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
                Everything you need to build, deploy, and scale AI applications with confidence.
                From intelligent routing to enterprise security, we've got you covered.
              </p>
            </motion.div>
          </div>
        </section>

        {/* Features Grid */}
        <section className="py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
              {features.map((feature, index) => (
                <motion.div
                  key={feature.title}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-white rounded-2xl p-8 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300"
                >
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0">
                      <div className="w-12 h-12 bg-[#ff6b35] rounded-xl flex items-center justify-center">
                        <feature.icon className="w-6 h-6 text-white" />
                      </div>
                    </div>
                    <div className="flex-1">
                      <h3 className="text-xl font-semibold text-gray-900 mb-3">
                        {feature.title}
                      </h3>
                      <p className="text-gray-600 mb-4">
                        {feature.description}
                      </p>
                      <ul className="space-y-2">
                        {feature.details.map((detail, idx) => (
                          <li key={idx} className="flex items-center text-sm text-gray-500">
                            <div className="w-1.5 h-1.5 bg-[#ff6b35] rounded-full mr-3"></div>
                            {detail}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gray-900">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              <h2 className="text-4xl font-bold text-white mb-6">
                Ready to Transform Your AI Development?
              </h2>
              <p className="text-xl text-gray-300 mb-8">
                Join thousands of developers who trust RouKey for their AI infrastructure.
              </p>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="bg-[#ff6b35] text-white px-8 py-4 rounded-xl font-semibold text-lg hover:bg-[#e55a2b] transition-colors duration-200 shadow-lg"
              >
                Get Started Now
              </motion.button>
            </motion.div>
          </div>
        </section>
      </main>

      <Suspense fallback={<div className="h-32 bg-gray-900"></div>}>
        <Footer />
      </Suspense>
    </div>
  );
}
