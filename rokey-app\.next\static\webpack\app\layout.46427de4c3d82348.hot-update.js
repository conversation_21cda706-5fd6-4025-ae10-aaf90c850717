"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"b637cf025c79\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcUm9LZXkgQXBwXFxyb2tleS1hcHBcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImI2MzdjZjAyNWM3OVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ConditionalLayout.tsx":
/*!**********************************************!*\
  !*** ./src/components/ConditionalLayout.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ConditionalLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/SidebarContext */ \"(app-pages-browser)/./src/contexts/SidebarContext.tsx\");\n/* harmony import */ var _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/NavigationContext */ \"(app-pages-browser)/./src/contexts/NavigationContext.tsx\");\n/* harmony import */ var _components_LayoutContent__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/LayoutContent */ \"(app-pages-browser)/./src/components/LayoutContent.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction ConditionalLayout(param) {\n    let { children } = param;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    // Check if this is a marketing page (landing, pricing, auth, features, about)\n    const isMarketingPage = pathname === '/' || pathname.startsWith('/pricing') || pathname.startsWith('/features') || pathname.startsWith('/about') || pathname.startsWith('/auth/');\n    // All pages need NavigationProvider for optimistic loading\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_3__.NavigationProvider, {\n        children: isMarketingPage ? // For marketing pages, render children directly without sidebar/navbar\n        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: children\n        }, void 0, false) : // For app pages, use the full layout with sidebar and navbar\n        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_2__.SidebarProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LayoutContent__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ConditionalLayout.tsx\",\n                lineNumber: 27,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ConditionalLayout.tsx\",\n            lineNumber: 26,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ConditionalLayout.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n_s(ConditionalLayout, \"xbyQPtUVMO7MNj7WjJlpdWqRcTo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname\n    ];\n});\n_c = ConditionalLayout;\nvar _c;\n$RefreshReg$(_c, \"ConditionalLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ConditionalLayout.tsx\n"));

/***/ })

});