"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/about/page",{

/***/ "(app-pages-browser)/./src/components/landing/LandingNavbar.tsx":
/*!**************************************************!*\
  !*** ./src/components/landing/LandingNavbar.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LandingNavbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _utils_cacheStrategy__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/cacheStrategy */ \"(app-pages-browser)/./src/utils/cacheStrategy.ts\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction LandingNavbar() {\n    _s();\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"fixed top-0 left-0 right-0 z-50 bg-black/90 backdrop-blur-md border-b border-gray-800\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\",\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-white rounded-lg flex items-center justify-center p-0.5\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            src: \"/roukey_logo.png\",\n                                            alt: \"RouKey\",\n                                            width: 28,\n                                            height: 28,\n                                            className: \"object-cover\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                            lineNumber: 21,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                        lineNumber: 20,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xl font-bold text-white\",\n                                        children: \"RouKey\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                        lineNumber: 29,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                lineNumber: 19,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/features\",\n                                    className: \"text-gray-300 hover:text-white transition-colors\",\n                                    onMouseEnter: ()=>_utils_cacheStrategy__WEBPACK_IMPORTED_MODULE_3__.prefetcher.schedulePrefetch('/features'),\n                                    children: \"Features\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                    lineNumber: 35,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/pricing\",\n                                    className: \"text-gray-300 hover:text-white transition-colors\",\n                                    onMouseEnter: ()=>_utils_cacheStrategy__WEBPACK_IMPORTED_MODULE_3__.prefetcher.schedulePrefetch('/pricing'),\n                                    children: \"Pricing\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/about\",\n                                    className: \"text-gray-300 hover:text-white transition-colors\",\n                                    onMouseEnter: ()=>_utils_cacheStrategy__WEBPACK_IMPORTED_MODULE_3__.prefetcher.schedulePrefetch('/about'),\n                                    children: \"About\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/auth/signin\",\n                                    className: \"text-gray-300 hover:text-white transition-colors\",\n                                    onMouseEnter: ()=>_utils_cacheStrategy__WEBPACK_IMPORTED_MODULE_3__.prefetcher.schedulePrefetch('/auth/signin'),\n                                    children: \"Sign In\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/auth/signup\",\n                                    className: \"bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white px-6 py-2 rounded-lg hover:shadow-lg hover:shadow-orange-500/25 transition-all duration-300 font-semibold\",\n                                    onMouseEnter: ()=>_utils_cacheStrategy__WEBPACK_IMPORTED_MODULE_3__.prefetcher.schedulePrefetch('/auth/signup'),\n                                    children: \"Get Started\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                                className: \"text-gray-300 hover:text-white transition-colors\",\n                                children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 9\n                }, this),\n                isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: -10\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    exit: {\n                        opacity: 0,\n                        y: -10\n                    },\n                    className: \"md:hidden py-4 border-t border-gray-800\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/features\",\n                                className: \"text-gray-300 hover:text-white transition-colors\",\n                                children: \"Features\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/pricing\",\n                                className: \"text-gray-300 hover:text-white transition-colors\",\n                                children: \"Pricing\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/about\",\n                                className: \"text-gray-300 hover:text-white transition-colors\",\n                                children: \"About\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/auth/signin\",\n                                className: \"text-gray-300 hover:text-white transition-colors\",\n                                children: \"Sign In\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/auth/signup\",\n                                className: \"bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white px-4 py-2 rounded-lg hover:shadow-lg hover:shadow-orange-500/25 transition-all duration-300 text-center font-semibold\",\n                                children: \"Get Started\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n_s(LandingNavbar, \"vK10R+uCyHfZ4DZVnxbYkMWJB8g=\");\n_c = LandingNavbar;\nvar _c;\n$RefreshReg$(_c, \"LandingNavbar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/LandingNavbar.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/utils/advancedCache.ts":
/*!************************************!*\
  !*** ./src/utils/advancedCache.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AdvancedCache: () => (/* binding */ AdvancedCache),\n/* harmony export */   cachedFetch: () => (/* binding */ cachedFetch),\n/* harmony export */   globalCache: () => (/* binding */ globalCache),\n/* harmony export */   useAdvancedCache: () => (/* binding */ useAdvancedCache)\n/* harmony export */ });\n// Phase 2B: Advanced Client-Side Caching System\nclass AdvancedCache {\n    // Set cache entry with advanced options\n    set(key, data) {\n        let options = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};\n        const { ttl = 300000, tags = [], priority = 'medium', serialize = false } = options;\n        // Serialize data if requested (useful for complex objects)\n        const cacheData = serialize ? JSON.parse(JSON.stringify(data)) : data;\n        const entry = {\n            data: cacheData,\n            timestamp: Date.now(),\n            ttl,\n            accessCount: 0,\n            lastAccessed: Date.now(),\n            tags,\n            priority\n        };\n        // Check if cache is full and evict if necessary\n        if (this.cache.size >= this.maxSize) {\n            this.evictLeastUsed();\n        }\n        this.cache.set(key, entry);\n    }\n    // Get cache entry with access tracking\n    get(key) {\n        const entry = this.cache.get(key);\n        if (!entry) {\n            return null;\n        }\n        // Check if expired\n        if (this.isExpired(entry)) {\n            this.cache.delete(key);\n            return null;\n        }\n        // Update access statistics\n        entry.accessCount++;\n        entry.lastAccessed = Date.now();\n        return entry.data;\n    }\n    // Get with stale-while-revalidate pattern\n    getStale(key) {\n        const entry = this.cache.get(key);\n        if (!entry) {\n            return {\n                data: null,\n                isStale: false\n            };\n        }\n        const isStale = this.isExpired(entry);\n        // Update access statistics even for stale data\n        entry.accessCount++;\n        entry.lastAccessed = Date.now();\n        return {\n            data: entry.data,\n            isStale\n        };\n    }\n    // Check if entry exists and is valid\n    has(key) {\n        const entry = this.cache.get(key);\n        if (!entry) return false;\n        if (this.isExpired(entry)) {\n            this.cache.delete(key);\n            return false;\n        }\n        return true;\n    }\n    // Delete specific entry\n    delete(key) {\n        return this.cache.delete(key);\n    }\n    // Invalidate by tags\n    invalidateByTags(tags) {\n        let invalidated = 0;\n        for (const [key, entry] of this.cache.entries()){\n            if (entry.tags.some((tag)=>tags.includes(tag))) {\n                this.cache.delete(key);\n                invalidated++;\n            }\n        }\n        return invalidated;\n    }\n    // Clear all cache\n    clear() {\n        this.cache.clear();\n    }\n    // Get cache statistics\n    getStats() {\n        const entries = Array.from(this.cache.values());\n        const now = Date.now();\n        return {\n            size: this.cache.size,\n            maxSize: this.maxSize,\n            hitRate: this.calculateHitRate(),\n            averageAge: entries.reduce((sum, entry)=>sum + (now - entry.timestamp), 0) / entries.length || 0,\n            totalAccesses: entries.reduce((sum, entry)=>sum + entry.accessCount, 0),\n            expiredEntries: entries.filter((entry)=>this.isExpired(entry)).length,\n            priorityDistribution: {\n                high: entries.filter((e)=>e.priority === 'high').length,\n                medium: entries.filter((e)=>e.priority === 'medium').length,\n                low: entries.filter((e)=>e.priority === 'low').length\n            }\n        };\n    }\n    // Preload data with background refresh\n    async preload(key, fetchFn) {\n        let options = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};\n        // Check if we have cached data\n        const cached = this.get(key);\n        if (cached) {\n            // Return cached data immediately, but refresh in background\n            this.backgroundRefresh(key, fetchFn, options);\n            return cached;\n        }\n        // No cached data, fetch and cache\n        const data = await fetchFn();\n        this.set(key, data, options);\n        return data;\n    }\n    // Background refresh for stale-while-revalidate\n    async backgroundRefresh(key, fetchFn, options) {\n        try {\n            const freshData = await fetchFn();\n            this.set(key, freshData, options);\n        } catch (error) {\n            console.warn(\"Background refresh failed for key \".concat(key, \":\"), error);\n        }\n    }\n    // Check if entry is expired\n    isExpired(entry) {\n        return Date.now() - entry.timestamp > entry.ttl;\n    }\n    // Evict least recently used entries\n    evictLeastUsed() {\n        if (this.cache.size === 0) return;\n        // Sort by priority first, then by last accessed time\n        const entries = Array.from(this.cache.entries()).sort((param, param1)=>{\n            let [, a] = param, [, b] = param1;\n            const priorityOrder = {\n                low: 0,\n                medium: 1,\n                high: 2\n            };\n            const priorityDiff = priorityOrder[a.priority] - priorityOrder[b.priority];\n            if (priorityDiff !== 0) return priorityDiff;\n            return a.lastAccessed - b.lastAccessed;\n        });\n        // Remove the least important/oldest entry\n        const [keyToRemove] = entries[0];\n        this.cache.delete(keyToRemove);\n    }\n    // Calculate hit rate (simplified)\n    calculateHitRate() {\n        const entries = Array.from(this.cache.values());\n        const totalAccesses = entries.reduce((sum, entry)=>sum + entry.accessCount, 0);\n        return totalAccesses > 0 ? this.cache.size / totalAccesses * 100 : 0;\n    }\n    // Start automatic cleanup\n    startCleanup() {\n        this.cleanupInterval = setInterval(()=>{\n            this.cleanup();\n        }, 60000); // Clean up every minute\n    }\n    // Clean up expired entries\n    cleanup() {\n        const now = Date.now();\n        const keysToDelete = [];\n        for (const [key, entry] of this.cache.entries()){\n            if (this.isExpired(entry)) {\n                keysToDelete.push(key);\n            }\n        }\n        keysToDelete.forEach((key)=>this.cache.delete(key));\n        if (keysToDelete.length > 0) {\n            console.log(\"[Cache] Cleaned up \".concat(keysToDelete.length, \" expired entries\"));\n        }\n    }\n    // Destroy cache and cleanup\n    destroy() {\n        if (this.cleanupInterval) {\n            clearInterval(this.cleanupInterval);\n        }\n        this.cache.clear();\n    }\n    constructor(maxSize = 100){\n        this.cache = new Map();\n        this.cleanupInterval = null;\n        this.maxSize = maxSize;\n        this.startCleanup();\n    }\n}\n// Global cache instance\nconst globalCache = new AdvancedCache(200);\n// React hook for cache management\nfunction useAdvancedCache() {\n    return {\n        set: globalCache.set.bind(globalCache),\n        get: globalCache.get.bind(globalCache),\n        getStale: globalCache.getStale.bind(globalCache),\n        has: globalCache.has.bind(globalCache),\n        delete: globalCache.delete.bind(globalCache),\n        invalidateByTags: globalCache.invalidateByTags.bind(globalCache),\n        clear: globalCache.clear.bind(globalCache),\n        preload: globalCache.preload.bind(globalCache),\n        getStats: globalCache.getStats.bind(globalCache)\n    };\n}\n// Utility function for API caching\nasync function cachedFetch(url) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const { cacheKey = url, cacheTTL = 300000, tags = [], ...fetchOptions } = options;\n    // Try to get from cache first\n    const cached = globalCache.get(cacheKey);\n    if (cached) {\n        return cached;\n    }\n    // Fetch from network\n    const response = await fetch(url, fetchOptions);\n    if (!response.ok) {\n        throw new Error(\"HTTP \".concat(response.status, \": \").concat(response.statusText));\n    }\n    const data = await response.json();\n    // Cache the result\n    globalCache.set(cacheKey, data, {\n        ttl: cacheTTL,\n        tags,\n        priority: 'medium'\n    });\n    return data;\n}\n// Export the cache instance\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/advancedCache.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/utils/cacheStrategy.ts":
/*!************************************!*\
  !*** ./src/utils/cacheStrategy.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CACHE_CONFIG: () => (/* binding */ CACHE_CONFIG),\n/* harmony export */   CACHE_KEYS: () => (/* binding */ CACHE_KEYS),\n/* harmony export */   CacheInvalidator: () => (/* binding */ CacheInvalidator),\n/* harmony export */   IntelligentPrefetcher: () => (/* binding */ IntelligentPrefetcher),\n/* harmony export */   cache: () => (/* reexport safe */ _advancedCache__WEBPACK_IMPORTED_MODULE_0__.globalCache),\n/* harmony export */   invalidator: () => (/* binding */ CacheInvalidator),\n/* harmony export */   prefetcher: () => (/* binding */ prefetcher),\n/* harmony export */   preloadLandingPageData: () => (/* binding */ preloadLandingPageData)\n/* harmony export */ });\n/* harmony import */ var _advancedCache__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./advancedCache */ \"(app-pages-browser)/./src/utils/advancedCache.ts\");\n/* __next_internal_client_entry_do_not_use__ CACHE_CONFIG,CACHE_KEYS,preloadLandingPageData,IntelligentPrefetcher,CacheInvalidator,prefetcher,cache,invalidator auto */ \n// Cache configuration for different data types\nconst CACHE_CONFIG = {\n    // Static content - long cache duration\n    static: {\n        ttl: 24 * 60 * 60 * 1000,\n        priority: 'high',\n        tags: [\n            'static'\n        ]\n    },\n    // API responses - medium cache duration\n    api: {\n        ttl: 5 * 60 * 1000,\n        priority: 'medium',\n        tags: [\n            'api'\n        ]\n    },\n    // User data - short cache duration\n    user: {\n        ttl: 2 * 60 * 1000,\n        priority: 'high',\n        tags: [\n            'user'\n        ]\n    },\n    // System status - very short cache\n    system: {\n        ttl: 30 * 1000,\n        priority: 'low',\n        tags: [\n            'system'\n        ]\n    },\n    // Pricing data - long cache (rarely changes)\n    pricing: {\n        ttl: 60 * 60 * 1000,\n        priority: 'medium',\n        tags: [\n            'pricing'\n        ]\n    }\n};\n// Cache keys for consistent naming\nconst CACHE_KEYS = {\n    // Landing page data\n    LANDING_FEATURES: 'landing:features',\n    LANDING_TESTIMONIALS: 'landing:testimonials',\n    LANDING_TRUST_BADGES: 'landing:trust-badges',\n    // Pricing data\n    PRICING_TIERS: 'pricing:tiers',\n    PRICING_COMPARISON: 'pricing:comparison',\n    // System data\n    SYSTEM_STATUS: 'system:status',\n    SYSTEM_MODELS: 'system:models',\n    // User data\n    USER_CONFIGS: 'user:configs',\n    USER_ANALYTICS: 'user:analytics',\n    USER_ACTIVITY: 'user:activity',\n    // Auth data\n    AUTH_SESSION: 'auth:session',\n    AUTH_PERMISSIONS: 'auth:permissions'\n};\n// Preload critical data for landing page\nasync function preloadLandingPageData() {\n    const criticalData = [\n        // Preload pricing data\n        {\n            key: CACHE_KEYS.PRICING_TIERS,\n            fetcher: ()=>fetch('/api/pricing/tiers').then((r)=>r.json()),\n            config: CACHE_CONFIG.pricing\n        },\n        // Preload system status\n        {\n            key: CACHE_KEYS.SYSTEM_STATUS,\n            fetcher: ()=>fetch('/api/system-status').then((r)=>r.json()),\n            config: CACHE_CONFIG.system\n        }\n    ];\n    const promises = criticalData.map(async (param)=>{\n        let { key, fetcher, config } = param;\n        try {\n            // Check if already cached\n            const cached = _advancedCache__WEBPACK_IMPORTED_MODULE_0__.globalCache.get(key);\n            if (cached) return cached;\n            // Fetch and cache\n            const data = await fetcher();\n            _advancedCache__WEBPACK_IMPORTED_MODULE_0__.globalCache.set(key, data, config);\n            return data;\n        } catch (error) {\n            console.warn(\"Failed to preload \".concat(key, \":\"), error);\n            return null;\n        }\n    });\n    await Promise.allSettled(promises);\n}\n// Intelligent prefetching based on user behavior\nclass IntelligentPrefetcher {\n    static getInstance() {\n        if (!IntelligentPrefetcher.instance) {\n            IntelligentPrefetcher.instance = new IntelligentPrefetcher();\n        }\n        return IntelligentPrefetcher.instance;\n    }\n    // Track user navigation patterns\n    trackNavigation(from, to) {\n        const pattern = \"\".concat(from, \"->\").concat(to);\n        const count = this.userBehavior.get(pattern) || 0;\n        this.userBehavior.set(pattern, count + 1);\n        // If pattern is common, prefetch related data\n        if (count > 2) {\n            this.schedulePrefetch(to);\n        }\n    }\n    // Schedule prefetch for a route\n    schedulePrefetch(route) {\n        if (this.prefetchQueue.has(route)) return;\n        this.prefetchQueue.add(route);\n        this.processPrefetchQueue();\n    }\n    // Process prefetch queue\n    async processPrefetchQueue() {\n        if (this.isProcessing || this.prefetchQueue.size === 0) return;\n        this.isProcessing = true;\n        for (const route of this.prefetchQueue){\n            try {\n                await this.prefetchRoute(route);\n                this.prefetchQueue.delete(route);\n            } catch (error) {\n                console.warn(\"Failed to prefetch \".concat(route, \":\"), error);\n            }\n            // Small delay to avoid overwhelming the browser\n            await new Promise((resolve)=>setTimeout(resolve, 100));\n        }\n        this.isProcessing = false;\n    }\n    // Prefetch data for a specific route\n    async prefetchRoute(route) {\n        const prefetchMap = {\n            '/dashboard': ()=>this.prefetchDashboardData(),\n            '/pricing': ()=>this.prefetchPricingData(),\n            '/auth/signup': ()=>this.prefetchAuthData(),\n            '/features': ()=>this.prefetchFeaturesData()\n        };\n        const prefetcher = prefetchMap[route];\n        if (prefetcher) {\n            await prefetcher();\n        }\n    }\n    // Prefetch dashboard data\n    async prefetchDashboardData() {\n        const promises = [\n            this.cacheIfNotExists(CACHE_KEYS.USER_CONFIGS, '/api/custom-configs', CACHE_CONFIG.user),\n            this.cacheIfNotExists(CACHE_KEYS.USER_ANALYTICS, '/api/analytics', CACHE_CONFIG.user),\n            this.cacheIfNotExists(CACHE_KEYS.SYSTEM_STATUS, '/api/system-status', CACHE_CONFIG.system)\n        ];\n        await Promise.allSettled(promises);\n    }\n    // Prefetch pricing data\n    async prefetchPricingData() {\n        const promises = [\n            this.cacheIfNotExists(CACHE_KEYS.PRICING_TIERS, '/api/pricing/tiers', CACHE_CONFIG.pricing),\n            this.cacheIfNotExists(CACHE_KEYS.PRICING_COMPARISON, '/api/pricing/comparison', CACHE_CONFIG.pricing)\n        ];\n        await Promise.allSettled(promises);\n    }\n    // Prefetch auth data\n    async prefetchAuthData() {\n        // Prefetch auth-related static data\n        const promises = [\n            this.cacheIfNotExists(CACHE_KEYS.PRICING_TIERS, '/api/pricing/tiers', CACHE_CONFIG.pricing)\n        ];\n        await Promise.allSettled(promises);\n    }\n    // Prefetch features data\n    async prefetchFeaturesData() {\n        const promises = [\n            this.cacheIfNotExists(CACHE_KEYS.LANDING_FEATURES, '/api/features', CACHE_CONFIG.static),\n            this.cacheIfNotExists(CACHE_KEYS.SYSTEM_MODELS, '/api/models', CACHE_CONFIG.static)\n        ];\n        await Promise.allSettled(promises);\n    }\n    // Helper to cache data if not already cached\n    async cacheIfNotExists(key, url, config) {\n        const cached = _advancedCache__WEBPACK_IMPORTED_MODULE_0__.globalCache.get(key);\n        if (cached) return cached;\n        try {\n            const response = await fetch(url);\n            if (response.ok) {\n                const data = await response.json();\n                _advancedCache__WEBPACK_IMPORTED_MODULE_0__.globalCache.set(key, data, config);\n                return data;\n            }\n        } catch (error) {\n            console.warn(\"Failed to cache \".concat(key, \":\"), error);\n        }\n    }\n    constructor(){\n        this.prefetchQueue = new Set();\n        this.isProcessing = false;\n        this.userBehavior = new Map();\n    }\n}\n// Cache invalidation strategies\nclass CacheInvalidator {\n    // Invalidate cache by tags\n    static invalidateByTags(tags) {\n        tags.forEach((tag)=>{\n            _advancedCache__WEBPACK_IMPORTED_MODULE_0__.globalCache.invalidateByTag(tag);\n        });\n    }\n    // Invalidate user-specific cache\n    static invalidateUserCache() {\n        this.invalidateByTags([\n            'user',\n            'auth'\n        ]);\n    }\n    // Invalidate system cache\n    static invalidateSystemCache() {\n        this.invalidateByTags([\n            'system'\n        ]);\n    }\n    // Smart invalidation based on data changes\n    static smartInvalidate(dataType, operation) {\n        const invalidationMap = {\n            'user-config': [\n                'user',\n                'api'\n            ],\n            'user-profile': [\n                'user',\n                'auth'\n            ],\n            'system-status': [\n                'system'\n            ],\n            'pricing': [\n                'pricing',\n                'static'\n            ]\n        };\n        const tagsToInvalidate = invalidationMap[dataType];\n        if (tagsToInvalidate) {\n            this.invalidateByTags(tagsToInvalidate);\n        }\n    }\n}\n// Initialize prefetcher\nconst prefetcher = IntelligentPrefetcher.getInstance();\n// Export cache utilities\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/cacheStrategy.ts\n"));

/***/ })

});