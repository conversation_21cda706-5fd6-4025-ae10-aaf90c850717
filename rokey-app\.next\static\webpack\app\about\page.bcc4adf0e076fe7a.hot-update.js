"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/about/page",{

/***/ "(app-pages-browser)/./src/components/OptimisticLink.tsx":
/*!*******************************************!*\
  !*** ./src/components/OptimisticLink.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OptimisticButton: () => (/* binding */ OptimisticButton),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useOptimisticNavigation: () => (/* binding */ useOptimisticNavigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/NavigationContext */ \"(app-pages-browser)/./src/contexts/NavigationContext.tsx\");\n/* harmony import */ var _utils_cacheStrategy__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/cacheStrategy */ \"(app-pages-browser)/./src/utils/cacheStrategy.ts\");\n/* __next_internal_client_entry_do_not_use__ default,OptimisticButton,useOptimisticNavigation auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\nconst OptimisticLink = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(_c = _s((param, ref)=>{\n    let { href, children, className = '', prefetch = true, onClick, onMouseEnter, ...props } = param;\n    _s();\n    // Use safe navigation hook that returns null instead of throwing\n    const navigationContext = (0,_contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_3__.useNavigationSafe)();\n    const { navigateOptimistically, isNavigating, targetRoute } = navigationContext || {\n        navigateOptimistically: ()=>{},\n        isNavigating: false,\n        targetRoute: null\n    };\n    const handleClick = (e)=>{\n        // Don't prevent default for external links or special cases\n        if (href.startsWith('http') || href.startsWith('mailto:') || href.startsWith('tel:')) {\n            onClick === null || onClick === void 0 ? void 0 : onClick(e);\n            return;\n        }\n        // Don't prevent default if user is holding modifier keys\n        if (e.metaKey || e.ctrlKey || e.shiftKey || e.altKey) {\n            onClick === null || onClick === void 0 ? void 0 : onClick(e);\n            return;\n        }\n        // Prevent default navigation and use optimistic loading\n        e.preventDefault();\n        // Call custom onClick if provided\n        onClick === null || onClick === void 0 ? void 0 : onClick(e);\n        // Start optimistic navigation\n        navigateOptimistically(href);\n    };\n    const handleMouseEnter = (e)=>{\n        // Prefetch on hover for better performance\n        if (prefetch && href.startsWith('/')) {\n            _utils_cacheStrategy__WEBPACK_IMPORTED_MODULE_4__.prefetcher.schedulePrefetch(href);\n        }\n        // Call custom onMouseEnter if provided\n        onMouseEnter === null || onMouseEnter === void 0 ? void 0 : onMouseEnter(e);\n    };\n    // Add loading state to className if this link is being navigated to\n    const isCurrentlyNavigating = isNavigating && targetRoute === href;\n    const finalClassName = \"\".concat(className, \" \").concat(isCurrentlyNavigating ? 'optimistic-loading' : '').trim();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n        ref: ref,\n        href: href,\n        className: finalClassName,\n        onClick: handleClick,\n        onMouseEnter: handleMouseEnter,\n        prefetch: prefetch,\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticLink.tsx\",\n        lineNumber: 83,\n        columnNumber: 7\n    }, undefined);\n}, \"v5FB7+SddS7k6fvmU/2wcDEM650=\", false, function() {\n    return [\n        _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_3__.useNavigationSafe\n    ];\n})), \"v5FB7+SddS7k6fvmU/2wcDEM650=\", false, function() {\n    return [\n        _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_3__.useNavigationSafe\n    ];\n});\n_c1 = OptimisticLink;\nOptimisticLink.displayName = 'OptimisticLink';\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (OptimisticLink);\nconst OptimisticButton = /*#__PURE__*/ _s1((0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(_c2 = _s1((param, ref)=>{\n    let { href, children, className = '', onClick, onMouseEnter, disabled = false, type = 'button', ...props } = param;\n    _s1();\n    // Use safe navigation hook that returns null instead of throwing\n    const navigationContext = (0,_contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_3__.useNavigationSafe)();\n    const { navigateOptimistically, isNavigating, targetRoute } = navigationContext || {\n        navigateOptimistically: ()=>{},\n        isNavigating: false,\n        targetRoute: null\n    };\n    const handleClick = (e)=>{\n        if (disabled) return;\n        // Call custom onClick first\n        onClick === null || onClick === void 0 ? void 0 : onClick(e);\n        // If href is provided, navigate optimistically\n        if (href && href.startsWith('/')) {\n            navigateOptimistically(href);\n        }\n    };\n    const handleMouseEnter = (e)=>{\n        // Prefetch on hover if href is provided\n        if (href && href.startsWith('/')) {\n            _utils_cacheStrategy__WEBPACK_IMPORTED_MODULE_4__.prefetcher.schedulePrefetch(href);\n        }\n        // Call custom onMouseEnter if provided\n        onMouseEnter === null || onMouseEnter === void 0 ? void 0 : onMouseEnter(e);\n    };\n    // Add loading state to className if this button's href is being navigated to\n    const isCurrentlyNavigating = isNavigating && targetRoute === href;\n    const finalClassName = \"\".concat(className, \" \").concat(isCurrentlyNavigating ? 'optimistic-loading' : '', \" \").concat(disabled ? 'opacity-50 cursor-not-allowed' : '').trim();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        ref: ref,\n        type: type,\n        className: finalClassName,\n        onClick: handleClick,\n        onMouseEnter: handleMouseEnter,\n        disabled: disabled,\n        ...props,\n        children: [\n            children,\n            isCurrentlyNavigating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"ml-2 inline-block w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticLink.tsx\",\n                lineNumber: 175,\n                columnNumber: 11\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticLink.tsx\",\n        lineNumber: 164,\n        columnNumber: 7\n    }, undefined);\n}, \"v5FB7+SddS7k6fvmU/2wcDEM650=\", false, function() {\n    return [\n        _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_3__.useNavigationSafe\n    ];\n})), \"v5FB7+SddS7k6fvmU/2wcDEM650=\", false, function() {\n    return [\n        _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_3__.useNavigationSafe\n    ];\n});\n_c3 = OptimisticButton;\nOptimisticButton.displayName = 'OptimisticButton';\n// Hook for programmatic optimistic navigation\nfunction useOptimisticNavigation() {\n    // Use try-catch to handle cases where NavigationProvider might not be available\n    let navigationContext;\n    try {\n        navigationContext = useNavigation();\n    } catch (error) {\n        // Fallback behavior if NavigationProvider is not available\n        console.warn('NavigationProvider not found, falling back to regular navigation behavior');\n        navigationContext = null;\n    }\n    const { navigateOptimistically, isNavigating, targetRoute, isPageCached } = navigationContext || {\n        navigateOptimistically: ()=>{},\n        isNavigating: false,\n        targetRoute: null,\n        isPageCached: ()=>false\n    };\n    const navigate = (href)=>{\n        if (href.startsWith('/')) {\n            navigateOptimistically(href);\n        } else {\n            // For external links, use regular navigation\n            window.location.href = href;\n        }\n    };\n    const isNavigatingTo = (href)=>{\n        return isNavigating && targetRoute === href;\n    };\n    const isCached = (href)=>{\n        return isPageCached(href);\n    };\n    return {\n        navigate,\n        isNavigating,\n        targetRoute,\n        isNavigatingTo,\n        isCached\n    };\n}\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"OptimisticLink$forwardRef\");\n$RefreshReg$(_c1, \"OptimisticLink\");\n$RefreshReg$(_c2, \"OptimisticButton$forwardRef\");\n$RefreshReg$(_c3, \"OptimisticButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL09wdGltaXN0aWNMaW5rLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFFMEQ7QUFDN0I7QUFDb0M7QUFDZDtBQW9CbkQsTUFBTUksK0JBQWlCSixHQUFBQSxpREFBVUEsU0FDL0IsUUFRR0s7UUFSRixFQUNDQyxJQUFJLEVBQ0pDLFFBQVEsRUFDUkMsWUFBWSxFQUFFLEVBQ2RDLFdBQVcsSUFBSSxFQUNmQyxPQUFPLEVBQ1BDLFlBQVksRUFDWixHQUFHQyxPQUNKOztJQUNDLGlFQUFpRTtJQUNqRSxNQUFNQyxvQkFBb0JYLDhFQUFpQkE7SUFFM0MsTUFBTSxFQUFFWSxzQkFBc0IsRUFBRUMsWUFBWSxFQUFFQyxXQUFXLEVBQUUsR0FBR0gscUJBQXFCO1FBQ2pGQyx3QkFBd0IsS0FBTztRQUMvQkMsY0FBYztRQUNkQyxhQUFhO0lBQ2Y7SUFFQSxNQUFNQyxjQUFjLENBQUNDO1FBQ25CLDREQUE0RDtRQUM1RCxJQUFJWixLQUFLYSxVQUFVLENBQUMsV0FBV2IsS0FBS2EsVUFBVSxDQUFDLGNBQWNiLEtBQUthLFVBQVUsQ0FBQyxTQUFTO1lBQ3BGVCxvQkFBQUEsOEJBQUFBLFFBQVVRO1lBQ1Y7UUFDRjtRQUVBLHlEQUF5RDtRQUN6RCxJQUFJQSxFQUFFRSxPQUFPLElBQUlGLEVBQUVHLE9BQU8sSUFBSUgsRUFBRUksUUFBUSxJQUFJSixFQUFFSyxNQUFNLEVBQUU7WUFDcERiLG9CQUFBQSw4QkFBQUEsUUFBVVE7WUFDVjtRQUNGO1FBRUEsd0RBQXdEO1FBQ3hEQSxFQUFFTSxjQUFjO1FBRWhCLGtDQUFrQztRQUNsQ2Qsb0JBQUFBLDhCQUFBQSxRQUFVUTtRQUVWLDhCQUE4QjtRQUM5QkosdUJBQXVCUjtJQUN6QjtJQUVBLE1BQU1tQixtQkFBbUIsQ0FBQ1A7UUFDeEIsMkNBQTJDO1FBQzNDLElBQUlULFlBQVlILEtBQUthLFVBQVUsQ0FBQyxNQUFNO1lBQ3BDaEIsNERBQVVBLENBQUN1QixnQkFBZ0IsQ0FBQ3BCO1FBQzlCO1FBRUEsdUNBQXVDO1FBQ3ZDSyx5QkFBQUEsbUNBQUFBLGFBQWVPO0lBQ2pCO0lBRUEsb0VBQW9FO0lBQ3BFLE1BQU1TLHdCQUF3QlosZ0JBQWdCQyxnQkFBZ0JWO0lBQzlELE1BQU1zQixpQkFBaUIsR0FBZ0JELE9BQWJuQixXQUFVLEtBQXFELE9BQWxEbUIsd0JBQXdCLHVCQUF1QixJQUFLRSxJQUFJO0lBRS9GLHFCQUNFLDhEQUFDNUIsa0RBQUlBO1FBQ0hJLEtBQUtBO1FBQ0xDLE1BQU1BO1FBQ05FLFdBQVdvQjtRQUNYbEIsU0FBU087UUFDVE4sY0FBY2M7UUFDZGhCLFVBQVVBO1FBQ1QsR0FBR0csS0FBSztrQkFFUkw7Ozs7OztBQUdQOztRQTFENEJMLDBFQUFpQkE7Ozs7UUFBakJBLDBFQUFpQkE7Ozs7QUE2RC9DRSxlQUFlMEIsV0FBVyxHQUFHO0FBRTdCLGlFQUFlMUIsY0FBY0EsRUFBQztBQWlCdkIsTUFBTTJCLGlDQUFtQi9CLElBQUFBLGlEQUFVQSxXQUN4QyxRQVNHSztRQVRGLEVBQ0NDLElBQUksRUFDSkMsUUFBUSxFQUNSQyxZQUFZLEVBQUUsRUFDZEUsT0FBTyxFQUNQQyxZQUFZLEVBQ1pxQixXQUFXLEtBQUssRUFDaEJDLE9BQU8sUUFBUSxFQUNmLEdBQUdyQixPQUNKOztJQUNDLGlFQUFpRTtJQUNqRSxNQUFNQyxvQkFBb0JYLDhFQUFpQkE7SUFFM0MsTUFBTSxFQUFFWSxzQkFBc0IsRUFBRUMsWUFBWSxFQUFFQyxXQUFXLEVBQUUsR0FBR0gscUJBQXFCO1FBQ2pGQyx3QkFBd0IsS0FBTztRQUMvQkMsY0FBYztRQUNkQyxhQUFhO0lBQ2Y7SUFFQSxNQUFNQyxjQUFjLENBQUNDO1FBQ25CLElBQUljLFVBQVU7UUFFZCw0QkFBNEI7UUFDNUJ0QixvQkFBQUEsOEJBQUFBLFFBQVVRO1FBRVYsK0NBQStDO1FBQy9DLElBQUlaLFFBQVFBLEtBQUthLFVBQVUsQ0FBQyxNQUFNO1lBQ2hDTCx1QkFBdUJSO1FBQ3pCO0lBQ0Y7SUFFQSxNQUFNbUIsbUJBQW1CLENBQUNQO1FBQ3hCLHdDQUF3QztRQUN4QyxJQUFJWixRQUFRQSxLQUFLYSxVQUFVLENBQUMsTUFBTTtZQUNoQ2hCLDREQUFVQSxDQUFDdUIsZ0JBQWdCLENBQUNwQjtRQUM5QjtRQUVBLHVDQUF1QztRQUN2Q0sseUJBQUFBLG1DQUFBQSxhQUFlTztJQUNqQjtJQUVBLDZFQUE2RTtJQUM3RSxNQUFNUyx3QkFBd0JaLGdCQUFnQkMsZ0JBQWdCVjtJQUM5RCxNQUFNc0IsaUJBQWlCLEdBQWdCRCxPQUFibkIsV0FBVSxLQUF3RHdCLE9BQXJETCx3QkFBd0IsdUJBQXVCLElBQUcsS0FBbUQsT0FBaERLLFdBQVcsa0NBQWtDLElBQUtILElBQUk7SUFFbEoscUJBQ0UsOERBQUNLO1FBQ0M3QixLQUFLQTtRQUNMNEIsTUFBTUE7UUFDTnpCLFdBQVdvQjtRQUNYbEIsU0FBU087UUFDVE4sY0FBY2M7UUFDZE8sVUFBVUE7UUFDVCxHQUFHcEIsS0FBSzs7WUFFUkw7WUFDQW9CLHVDQUNDLDhEQUFDUTtnQkFBSzNCLFdBQVU7Ozs7Ozs7Ozs7OztBQUl4Qjs7UUFsRDRCTiwwRUFBaUJBOzs7O1FBQWpCQSwwRUFBaUJBOztHQW1EN0M7O0FBRUY2QixpQkFBaUJELFdBQVcsR0FBRztBQUUvQiw4Q0FBOEM7QUFDdkMsU0FBU007SUFDZCxnRkFBZ0Y7SUFDaEYsSUFBSXZCO0lBQ0osSUFBSTtRQUNGQSxvQkFBb0J3QjtJQUN0QixFQUFFLE9BQU9DLE9BQU87UUFDZCwyREFBMkQ7UUFDM0RDLFFBQVFDLElBQUksQ0FBQztRQUNiM0Isb0JBQW9CO0lBQ3RCO0lBRUEsTUFBTSxFQUFFQyxzQkFBc0IsRUFBRUMsWUFBWSxFQUFFQyxXQUFXLEVBQUV5QixZQUFZLEVBQUUsR0FBRzVCLHFCQUFxQjtRQUMvRkMsd0JBQXdCLEtBQU87UUFDL0JDLGNBQWM7UUFDZEMsYUFBYTtRQUNieUIsY0FBYyxJQUFNO0lBQ3RCO0lBRUEsTUFBTUMsV0FBVyxDQUFDcEM7UUFDaEIsSUFBSUEsS0FBS2EsVUFBVSxDQUFDLE1BQU07WUFDeEJMLHVCQUF1QlI7UUFDekIsT0FBTztZQUNMLDZDQUE2QztZQUM3Q3FDLE9BQU9DLFFBQVEsQ0FBQ3RDLElBQUksR0FBR0E7UUFDekI7SUFDRjtJQUVBLE1BQU11QyxpQkFBaUIsQ0FBQ3ZDO1FBQ3RCLE9BQU9TLGdCQUFnQkMsZ0JBQWdCVjtJQUN6QztJQUVBLE1BQU13QyxXQUFXLENBQUN4QztRQUNoQixPQUFPbUMsYUFBYW5DO0lBQ3RCO0lBRUEsT0FBTztRQUNMb0M7UUFDQTNCO1FBQ0FDO1FBQ0E2QjtRQUNBQztJQUNGO0FBQ0YiLCJzb3VyY2VzIjpbIkM6XFxSb0tleSBBcHBcXHJva2V5LWFwcFxcc3JjXFxjb21wb25lbnRzXFxPcHRpbWlzdGljTGluay50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyBmb3J3YXJkUmVmLCBNb3VzZUV2ZW50LCBSZWFjdE5vZGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgTGluayBmcm9tICduZXh0L2xpbmsnO1xuaW1wb3J0IHsgdXNlTmF2aWdhdGlvblNhZmUgfSBmcm9tICdAL2NvbnRleHRzL05hdmlnYXRpb25Db250ZXh0JztcbmltcG9ydCB7IHByZWZldGNoZXIgfSBmcm9tICdAL3V0aWxzL2NhY2hlU3RyYXRlZ3knO1xuXG5pbnRlcmZhY2UgT3B0aW1pc3RpY0xpbmtQcm9wcyB7XG4gIGhyZWY6IHN0cmluZztcbiAgY2hpbGRyZW46IFJlYWN0Tm9kZTtcbiAgY2xhc3NOYW1lPzogc3RyaW5nO1xuICBwcmVmZXRjaD86IGJvb2xlYW47XG4gIHJlcGxhY2U/OiBib29sZWFuO1xuICBzY3JvbGw/OiBib29sZWFuO1xuICBzaGFsbG93PzogYm9vbGVhbjtcbiAgb25DbGljaz86IChlOiBNb3VzZUV2ZW50PEhUTUxBbmNob3JFbGVtZW50PikgPT4gdm9pZDtcbiAgb25Nb3VzZUVudGVyPzogKGU6IE1vdXNlRXZlbnQ8SFRNTEFuY2hvckVsZW1lbnQ+KSA9PiB2b2lkO1xuICB0YXJnZXQ/OiBzdHJpbmc7XG4gIHJlbD86IHN0cmluZztcbiAgJ2FyaWEtbGFiZWwnPzogc3RyaW5nO1xuICBpZD86IHN0cmluZztcbiAgcm9sZT86IHN0cmluZztcbiAgdGFiSW5kZXg/OiBudW1iZXI7XG59XG5cbmNvbnN0IE9wdGltaXN0aWNMaW5rID0gZm9yd2FyZFJlZjxIVE1MQW5jaG9yRWxlbWVudCwgT3B0aW1pc3RpY0xpbmtQcm9wcz4oXG4gICh7XG4gICAgaHJlZixcbiAgICBjaGlsZHJlbixcbiAgICBjbGFzc05hbWUgPSAnJyxcbiAgICBwcmVmZXRjaCA9IHRydWUsXG4gICAgb25DbGljayxcbiAgICBvbk1vdXNlRW50ZXIsXG4gICAgLi4ucHJvcHNcbiAgfSwgcmVmKSA9PiB7XG4gICAgLy8gVXNlIHNhZmUgbmF2aWdhdGlvbiBob29rIHRoYXQgcmV0dXJucyBudWxsIGluc3RlYWQgb2YgdGhyb3dpbmdcbiAgICBjb25zdCBuYXZpZ2F0aW9uQ29udGV4dCA9IHVzZU5hdmlnYXRpb25TYWZlKCk7XG5cbiAgICBjb25zdCB7IG5hdmlnYXRlT3B0aW1pc3RpY2FsbHksIGlzTmF2aWdhdGluZywgdGFyZ2V0Um91dGUgfSA9IG5hdmlnYXRpb25Db250ZXh0IHx8IHtcbiAgICAgIG5hdmlnYXRlT3B0aW1pc3RpY2FsbHk6ICgpID0+IHt9LFxuICAgICAgaXNOYXZpZ2F0aW5nOiBmYWxzZSxcbiAgICAgIHRhcmdldFJvdXRlOiBudWxsXG4gICAgfTtcblxuICAgIGNvbnN0IGhhbmRsZUNsaWNrID0gKGU6IE1vdXNlRXZlbnQ8SFRNTEFuY2hvckVsZW1lbnQ+KSA9PiB7XG4gICAgICAvLyBEb24ndCBwcmV2ZW50IGRlZmF1bHQgZm9yIGV4dGVybmFsIGxpbmtzIG9yIHNwZWNpYWwgY2FzZXNcbiAgICAgIGlmIChocmVmLnN0YXJ0c1dpdGgoJ2h0dHAnKSB8fCBocmVmLnN0YXJ0c1dpdGgoJ21haWx0bzonKSB8fCBocmVmLnN0YXJ0c1dpdGgoJ3RlbDonKSkge1xuICAgICAgICBvbkNsaWNrPy4oZSk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgLy8gRG9uJ3QgcHJldmVudCBkZWZhdWx0IGlmIHVzZXIgaXMgaG9sZGluZyBtb2RpZmllciBrZXlzXG4gICAgICBpZiAoZS5tZXRhS2V5IHx8IGUuY3RybEtleSB8fCBlLnNoaWZ0S2V5IHx8IGUuYWx0S2V5KSB7XG4gICAgICAgIG9uQ2xpY2s/LihlKTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuXG4gICAgICAvLyBQcmV2ZW50IGRlZmF1bHQgbmF2aWdhdGlvbiBhbmQgdXNlIG9wdGltaXN0aWMgbG9hZGluZ1xuICAgICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xuICAgICAgXG4gICAgICAvLyBDYWxsIGN1c3RvbSBvbkNsaWNrIGlmIHByb3ZpZGVkXG4gICAgICBvbkNsaWNrPy4oZSk7XG4gICAgICBcbiAgICAgIC8vIFN0YXJ0IG9wdGltaXN0aWMgbmF2aWdhdGlvblxuICAgICAgbmF2aWdhdGVPcHRpbWlzdGljYWxseShocmVmKTtcbiAgICB9O1xuXG4gICAgY29uc3QgaGFuZGxlTW91c2VFbnRlciA9IChlOiBNb3VzZUV2ZW50PEhUTUxBbmNob3JFbGVtZW50PikgPT4ge1xuICAgICAgLy8gUHJlZmV0Y2ggb24gaG92ZXIgZm9yIGJldHRlciBwZXJmb3JtYW5jZVxuICAgICAgaWYgKHByZWZldGNoICYmIGhyZWYuc3RhcnRzV2l0aCgnLycpKSB7XG4gICAgICAgIHByZWZldGNoZXIuc2NoZWR1bGVQcmVmZXRjaChocmVmKTtcbiAgICAgIH1cbiAgICAgIFxuICAgICAgLy8gQ2FsbCBjdXN0b20gb25Nb3VzZUVudGVyIGlmIHByb3ZpZGVkXG4gICAgICBvbk1vdXNlRW50ZXI/LihlKTtcbiAgICB9O1xuXG4gICAgLy8gQWRkIGxvYWRpbmcgc3RhdGUgdG8gY2xhc3NOYW1lIGlmIHRoaXMgbGluayBpcyBiZWluZyBuYXZpZ2F0ZWQgdG9cbiAgICBjb25zdCBpc0N1cnJlbnRseU5hdmlnYXRpbmcgPSBpc05hdmlnYXRpbmcgJiYgdGFyZ2V0Um91dGUgPT09IGhyZWY7XG4gICAgY29uc3QgZmluYWxDbGFzc05hbWUgPSBgJHtjbGFzc05hbWV9ICR7aXNDdXJyZW50bHlOYXZpZ2F0aW5nID8gJ29wdGltaXN0aWMtbG9hZGluZycgOiAnJ31gLnRyaW0oKTtcblxuICAgIHJldHVybiAoXG4gICAgICA8TGlua1xuICAgICAgICByZWY9e3JlZn1cbiAgICAgICAgaHJlZj17aHJlZn1cbiAgICAgICAgY2xhc3NOYW1lPXtmaW5hbENsYXNzTmFtZX1cbiAgICAgICAgb25DbGljaz17aGFuZGxlQ2xpY2t9XG4gICAgICAgIG9uTW91c2VFbnRlcj17aGFuZGxlTW91c2VFbnRlcn1cbiAgICAgICAgcHJlZmV0Y2g9e3ByZWZldGNofVxuICAgICAgICB7Li4ucHJvcHN9XG4gICAgICA+XG4gICAgICAgIHtjaGlsZHJlbn1cbiAgICAgIDwvTGluaz5cbiAgICApO1xuICB9XG4pO1xuXG5PcHRpbWlzdGljTGluay5kaXNwbGF5TmFtZSA9ICdPcHRpbWlzdGljTGluayc7XG5cbmV4cG9ydCBkZWZhdWx0IE9wdGltaXN0aWNMaW5rO1xuXG4vLyBCdXR0b24gY29tcG9uZW50IHdpdGggb3B0aW1pc3RpYyBuYXZpZ2F0aW9uXG5pbnRlcmZhY2UgT3B0aW1pc3RpY0J1dHRvblByb3BzIHtcbiAgaHJlZj86IHN0cmluZztcbiAgY2hpbGRyZW46IFJlYWN0Tm9kZTtcbiAgY2xhc3NOYW1lPzogc3RyaW5nO1xuICBvbkNsaWNrPzogKGU6IE1vdXNlRXZlbnQ8SFRNTEJ1dHRvbkVsZW1lbnQ+KSA9PiB2b2lkO1xuICBvbk1vdXNlRW50ZXI/OiAoZTogTW91c2VFdmVudDxIVE1MQnV0dG9uRWxlbWVudD4pID0+IHZvaWQ7XG4gIGRpc2FibGVkPzogYm9vbGVhbjtcbiAgdHlwZT86ICdidXR0b24nIHwgJ3N1Ym1pdCcgfCAncmVzZXQnO1xuICAnYXJpYS1sYWJlbCc/OiBzdHJpbmc7XG4gIGlkPzogc3RyaW5nO1xuICByb2xlPzogc3RyaW5nO1xuICB0YWJJbmRleD86IG51bWJlcjtcbn1cblxuZXhwb3J0IGNvbnN0IE9wdGltaXN0aWNCdXR0b24gPSBmb3J3YXJkUmVmPEhUTUxCdXR0b25FbGVtZW50LCBPcHRpbWlzdGljQnV0dG9uUHJvcHM+KFxuICAoe1xuICAgIGhyZWYsXG4gICAgY2hpbGRyZW4sXG4gICAgY2xhc3NOYW1lID0gJycsXG4gICAgb25DbGljayxcbiAgICBvbk1vdXNlRW50ZXIsXG4gICAgZGlzYWJsZWQgPSBmYWxzZSxcbiAgICB0eXBlID0gJ2J1dHRvbicsXG4gICAgLi4ucHJvcHNcbiAgfSwgcmVmKSA9PiB7XG4gICAgLy8gVXNlIHNhZmUgbmF2aWdhdGlvbiBob29rIHRoYXQgcmV0dXJucyBudWxsIGluc3RlYWQgb2YgdGhyb3dpbmdcbiAgICBjb25zdCBuYXZpZ2F0aW9uQ29udGV4dCA9IHVzZU5hdmlnYXRpb25TYWZlKCk7XG5cbiAgICBjb25zdCB7IG5hdmlnYXRlT3B0aW1pc3RpY2FsbHksIGlzTmF2aWdhdGluZywgdGFyZ2V0Um91dGUgfSA9IG5hdmlnYXRpb25Db250ZXh0IHx8IHtcbiAgICAgIG5hdmlnYXRlT3B0aW1pc3RpY2FsbHk6ICgpID0+IHt9LFxuICAgICAgaXNOYXZpZ2F0aW5nOiBmYWxzZSxcbiAgICAgIHRhcmdldFJvdXRlOiBudWxsXG4gICAgfTtcblxuICAgIGNvbnN0IGhhbmRsZUNsaWNrID0gKGU6IE1vdXNlRXZlbnQ8SFRNTEJ1dHRvbkVsZW1lbnQ+KSA9PiB7XG4gICAgICBpZiAoZGlzYWJsZWQpIHJldHVybjtcbiAgICAgIFxuICAgICAgLy8gQ2FsbCBjdXN0b20gb25DbGljayBmaXJzdFxuICAgICAgb25DbGljaz8uKGUpO1xuICAgICAgXG4gICAgICAvLyBJZiBocmVmIGlzIHByb3ZpZGVkLCBuYXZpZ2F0ZSBvcHRpbWlzdGljYWxseVxuICAgICAgaWYgKGhyZWYgJiYgaHJlZi5zdGFydHNXaXRoKCcvJykpIHtcbiAgICAgICAgbmF2aWdhdGVPcHRpbWlzdGljYWxseShocmVmKTtcbiAgICAgIH1cbiAgICB9O1xuXG4gICAgY29uc3QgaGFuZGxlTW91c2VFbnRlciA9IChlOiBNb3VzZUV2ZW50PEhUTUxCdXR0b25FbGVtZW50PikgPT4ge1xuICAgICAgLy8gUHJlZmV0Y2ggb24gaG92ZXIgaWYgaHJlZiBpcyBwcm92aWRlZFxuICAgICAgaWYgKGhyZWYgJiYgaHJlZi5zdGFydHNXaXRoKCcvJykpIHtcbiAgICAgICAgcHJlZmV0Y2hlci5zY2hlZHVsZVByZWZldGNoKGhyZWYpO1xuICAgICAgfVxuICAgICAgXG4gICAgICAvLyBDYWxsIGN1c3RvbSBvbk1vdXNlRW50ZXIgaWYgcHJvdmlkZWRcbiAgICAgIG9uTW91c2VFbnRlcj8uKGUpO1xuICAgIH07XG5cbiAgICAvLyBBZGQgbG9hZGluZyBzdGF0ZSB0byBjbGFzc05hbWUgaWYgdGhpcyBidXR0b24ncyBocmVmIGlzIGJlaW5nIG5hdmlnYXRlZCB0b1xuICAgIGNvbnN0IGlzQ3VycmVudGx5TmF2aWdhdGluZyA9IGlzTmF2aWdhdGluZyAmJiB0YXJnZXRSb3V0ZSA9PT0gaHJlZjtcbiAgICBjb25zdCBmaW5hbENsYXNzTmFtZSA9IGAke2NsYXNzTmFtZX0gJHtpc0N1cnJlbnRseU5hdmlnYXRpbmcgPyAnb3B0aW1pc3RpYy1sb2FkaW5nJyA6ICcnfSAke2Rpc2FibGVkID8gJ29wYWNpdHktNTAgY3Vyc29yLW5vdC1hbGxvd2VkJyA6ICcnfWAudHJpbSgpO1xuXG4gICAgcmV0dXJuIChcbiAgICAgIDxidXR0b25cbiAgICAgICAgcmVmPXtyZWZ9XG4gICAgICAgIHR5cGU9e3R5cGV9XG4gICAgICAgIGNsYXNzTmFtZT17ZmluYWxDbGFzc05hbWV9XG4gICAgICAgIG9uQ2xpY2s9e2hhbmRsZUNsaWNrfVxuICAgICAgICBvbk1vdXNlRW50ZXI9e2hhbmRsZU1vdXNlRW50ZXJ9XG4gICAgICAgIGRpc2FibGVkPXtkaXNhYmxlZH1cbiAgICAgICAgey4uLnByb3BzfVxuICAgICAgPlxuICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgIHtpc0N1cnJlbnRseU5hdmlnYXRpbmcgJiYgKFxuICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1sLTIgaW5saW5lLWJsb2NrIHctNCBoLTQgYm9yZGVyLTIgYm9yZGVyLWN1cnJlbnQgYm9yZGVyLXQtdHJhbnNwYXJlbnQgcm91bmRlZC1mdWxsIGFuaW1hdGUtc3BpblwiIC8+XG4gICAgICAgICl9XG4gICAgICA8L2J1dHRvbj5cbiAgICApO1xuICB9XG4pO1xuXG5PcHRpbWlzdGljQnV0dG9uLmRpc3BsYXlOYW1lID0gJ09wdGltaXN0aWNCdXR0b24nO1xuXG4vLyBIb29rIGZvciBwcm9ncmFtbWF0aWMgb3B0aW1pc3RpYyBuYXZpZ2F0aW9uXG5leHBvcnQgZnVuY3Rpb24gdXNlT3B0aW1pc3RpY05hdmlnYXRpb24oKSB7XG4gIC8vIFVzZSB0cnktY2F0Y2ggdG8gaGFuZGxlIGNhc2VzIHdoZXJlIE5hdmlnYXRpb25Qcm92aWRlciBtaWdodCBub3QgYmUgYXZhaWxhYmxlXG4gIGxldCBuYXZpZ2F0aW9uQ29udGV4dDtcbiAgdHJ5IHtcbiAgICBuYXZpZ2F0aW9uQ29udGV4dCA9IHVzZU5hdmlnYXRpb24oKTtcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAvLyBGYWxsYmFjayBiZWhhdmlvciBpZiBOYXZpZ2F0aW9uUHJvdmlkZXIgaXMgbm90IGF2YWlsYWJsZVxuICAgIGNvbnNvbGUud2FybignTmF2aWdhdGlvblByb3ZpZGVyIG5vdCBmb3VuZCwgZmFsbGluZyBiYWNrIHRvIHJlZ3VsYXIgbmF2aWdhdGlvbiBiZWhhdmlvcicpO1xuICAgIG5hdmlnYXRpb25Db250ZXh0ID0gbnVsbDtcbiAgfVxuXG4gIGNvbnN0IHsgbmF2aWdhdGVPcHRpbWlzdGljYWxseSwgaXNOYXZpZ2F0aW5nLCB0YXJnZXRSb3V0ZSwgaXNQYWdlQ2FjaGVkIH0gPSBuYXZpZ2F0aW9uQ29udGV4dCB8fCB7XG4gICAgbmF2aWdhdGVPcHRpbWlzdGljYWxseTogKCkgPT4ge30sXG4gICAgaXNOYXZpZ2F0aW5nOiBmYWxzZSxcbiAgICB0YXJnZXRSb3V0ZTogbnVsbCxcbiAgICBpc1BhZ2VDYWNoZWQ6ICgpID0+IGZhbHNlXG4gIH07XG5cbiAgY29uc3QgbmF2aWdhdGUgPSAoaHJlZjogc3RyaW5nKSA9PiB7XG4gICAgaWYgKGhyZWYuc3RhcnRzV2l0aCgnLycpKSB7XG4gICAgICBuYXZpZ2F0ZU9wdGltaXN0aWNhbGx5KGhyZWYpO1xuICAgIH0gZWxzZSB7XG4gICAgICAvLyBGb3IgZXh0ZXJuYWwgbGlua3MsIHVzZSByZWd1bGFyIG5hdmlnYXRpb25cbiAgICAgIHdpbmRvdy5sb2NhdGlvbi5ocmVmID0gaHJlZjtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaXNOYXZpZ2F0aW5nVG8gPSAoaHJlZjogc3RyaW5nKSA9PiB7XG4gICAgcmV0dXJuIGlzTmF2aWdhdGluZyAmJiB0YXJnZXRSb3V0ZSA9PT0gaHJlZjtcbiAgfTtcblxuICBjb25zdCBpc0NhY2hlZCA9IChocmVmOiBzdHJpbmcpID0+IHtcbiAgICByZXR1cm4gaXNQYWdlQ2FjaGVkKGhyZWYpO1xuICB9O1xuXG4gIHJldHVybiB7XG4gICAgbmF2aWdhdGUsXG4gICAgaXNOYXZpZ2F0aW5nLFxuICAgIHRhcmdldFJvdXRlLFxuICAgIGlzTmF2aWdhdGluZ1RvLFxuICAgIGlzQ2FjaGVkXG4gIH07XG59XG4iXSwibmFtZXMiOlsiZm9yd2FyZFJlZiIsIkxpbmsiLCJ1c2VOYXZpZ2F0aW9uU2FmZSIsInByZWZldGNoZXIiLCJPcHRpbWlzdGljTGluayIsInJlZiIsImhyZWYiLCJjaGlsZHJlbiIsImNsYXNzTmFtZSIsInByZWZldGNoIiwib25DbGljayIsIm9uTW91c2VFbnRlciIsInByb3BzIiwibmF2aWdhdGlvbkNvbnRleHQiLCJuYXZpZ2F0ZU9wdGltaXN0aWNhbGx5IiwiaXNOYXZpZ2F0aW5nIiwidGFyZ2V0Um91dGUiLCJoYW5kbGVDbGljayIsImUiLCJzdGFydHNXaXRoIiwibWV0YUtleSIsImN0cmxLZXkiLCJzaGlmdEtleSIsImFsdEtleSIsInByZXZlbnREZWZhdWx0IiwiaGFuZGxlTW91c2VFbnRlciIsInNjaGVkdWxlUHJlZmV0Y2giLCJpc0N1cnJlbnRseU5hdmlnYXRpbmciLCJmaW5hbENsYXNzTmFtZSIsInRyaW0iLCJkaXNwbGF5TmFtZSIsIk9wdGltaXN0aWNCdXR0b24iLCJkaXNhYmxlZCIsInR5cGUiLCJidXR0b24iLCJzcGFuIiwidXNlT3B0aW1pc3RpY05hdmlnYXRpb24iLCJ1c2VOYXZpZ2F0aW9uIiwiZXJyb3IiLCJjb25zb2xlIiwid2FybiIsImlzUGFnZUNhY2hlZCIsIm5hdmlnYXRlIiwid2luZG93IiwibG9jYXRpb24iLCJpc05hdmlnYXRpbmdUbyIsImlzQ2FjaGVkIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/OptimisticLink.tsx\n"));

/***/ })

});