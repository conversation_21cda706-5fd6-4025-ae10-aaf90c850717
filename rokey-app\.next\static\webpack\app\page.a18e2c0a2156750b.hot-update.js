"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/landing/TrustBadges.tsx":
/*!************************************************!*\
  !*** ./src/components/landing/TrustBadges.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TrustBadges)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst useCountUp = function(end) {\n    let duration = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 2000, delay = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 0;\n    _s();\n    const [count, setCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [hasStarted, setHasStarted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useCountUp.useEffect\": ()=>{\n            if (!hasStarted) return;\n            const startTime = Date.now() + delay;\n            const endTime = startTime + duration;\n            const timer = setInterval({\n                \"useCountUp.useEffect.timer\": ()=>{\n                    const now = Date.now();\n                    if (now < startTime) return;\n                    const progress = Math.min((now - startTime) / duration, 1);\n                    const easeOutQuart = 1 - Math.pow(1 - progress, 4);\n                    setCount(Math.floor(easeOutQuart * end));\n                    if (progress === 1) {\n                        clearInterval(timer);\n                    }\n                }\n            }[\"useCountUp.useEffect.timer\"], 16);\n            return ({\n                \"useCountUp.useEffect\": ()=>clearInterval(timer)\n            })[\"useCountUp.useEffect\"];\n        }\n    }[\"useCountUp.useEffect\"], [\n        end,\n        duration,\n        delay,\n        hasStarted\n    ]);\n    return {\n        count,\n        startCounting: ()=>setHasStarted(true)\n    };\n};\n_s(useCountUp, \"TCnTLIIeaoJq9uh8nHvQ9hiGmWQ=\");\nfunction TrustBadges() {\n    _s1();\n    const modelsCount = useCountUp(300, 2000, 200);\n    const requestsCount = useCountUp(10, 2000, 400); // 10M\n    const developersCount = useCountUp(5000, 2000, 600);\n    const uptimeCount = useCountUp(99.9, 2000, 800);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative py-20 bg-gradient-to-r from-[#ff6b35] via-[#f7931e] to-[#ff6b35] overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-10\",\n                style: {\n                    backgroundImage: \"\\n            linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),\\n            linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px)\\n          \",\n                    backgroundSize: '50px 50px'\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TrustBadges.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        onViewportEnter: ()=>{\n                            developersCount.startCounting();\n                            requestsCount.startCounting();\n                            uptimeCount.startCounting();\n                            modelsCount.startCounting();\n                        },\n                        className: \"text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-white/90 text-2xl font-bold mb-3\",\n                                children: \"Powering AI Innovation Worldwide\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TrustBadges.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/70 text-lg\",\n                                children: \"Join thousands of developers enjoying unlimited AI access with RouKey\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TrustBadges.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TrustBadges.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        transition: {\n                            delay: 0.3,\n                            duration: 0.6\n                        },\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-8 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                className: \"group\",\n                                whileHover: {\n                                    scale: 1.05\n                                },\n                                transition: {\n                                    type: \"spring\",\n                                    stiffness: 300\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-4xl md:text-5xl font-bold text-white mb-3 group-hover:text-white/90 transition-colors duration-300\",\n                                        children: [\n                                            modelsCount.count,\n                                            \"+\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TrustBadges.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-white/80 text-base font-medium\",\n                                        children: \"AI Models Supported\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TrustBadges.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TrustBadges.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                className: \"group\",\n                                whileHover: {\n                                    scale: 1.05\n                                },\n                                transition: {\n                                    type: \"spring\",\n                                    stiffness: 300\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-4xl md:text-5xl font-bold text-white mb-3 group-hover:text-white/90 transition-colors duration-300\",\n                                        children: [\n                                            requestsCount.count,\n                                            \"M+\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TrustBadges.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-white/80 text-base font-medium\",\n                                        children: \"API Requests Processed\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TrustBadges.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TrustBadges.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                className: \"group\",\n                                whileHover: {\n                                    scale: 1.05\n                                },\n                                transition: {\n                                    type: \"spring\",\n                                    stiffness: 300\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-4xl md:text-5xl font-bold text-white mb-3 group-hover:text-white/90 transition-colors duration-300\",\n                                        children: [\n                                            developersCount.count.toLocaleString(),\n                                            \"+\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TrustBadges.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-white/80 text-base font-medium\",\n                                        children: \"Developers Trust Us\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TrustBadges.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TrustBadges.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                className: \"group\",\n                                whileHover: {\n                                    scale: 1.05\n                                },\n                                transition: {\n                                    type: \"spring\",\n                                    stiffness: 300\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-4xl md:text-5xl font-bold text-white mb-3 group-hover:text-white/90 transition-colors duration-300\",\n                                        children: [\n                                            uptimeCount.count.toFixed(1),\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TrustBadges.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-white/80 text-base font-medium\",\n                                        children: \"Uptime Guarantee\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TrustBadges.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TrustBadges.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TrustBadges.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TrustBadges.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TrustBadges.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, this);\n}\n_s1(TrustBadges, \"rswsUbxTnFV5FhRpROtEaKg43w0=\", false, function() {\n    return [\n        useCountUp,\n        useCountUp,\n        useCountUp,\n        useCountUp\n    ];\n});\n_c = TrustBadges;\nvar _c;\n$RefreshReg$(_c, \"TrustBadges\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/TrustBadges.tsx\n"));

/***/ })

});