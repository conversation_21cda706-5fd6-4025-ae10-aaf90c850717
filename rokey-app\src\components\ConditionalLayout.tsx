'use client';

import { usePathname } from 'next/navigation';
import { SidebarProvider } from "@/contexts/SidebarContext";
import { NavigationProvider } from "@/contexts/NavigationContext";
import LayoutContent from "@/components/LayoutContent";

export default function ConditionalLayout({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();

  // Check if this is a marketing page (landing, pricing, auth, features, about)
  const isMarketingPage = pathname === '/' ||
                          pathname.startsWith('/pricing') ||
                          pathname.startsWith('/features') ||
                          pathname.startsWith('/about') ||
                          pathname.startsWith('/auth/');

  // All pages need NavigationProvider for optimistic loading
  return (
    <NavigationProvider>
      {isMarketingPage ? (
        // For marketing pages, render children directly without sidebar/navbar
        <>{children}</>
      ) : (
        // For app pages, use the full layout with sidebar and navbar
        <SidebarProvider>
          <LayoutContent>
            {children}
          </LayoutContent>
        </SidebarProvider>
      )}
    </NavigationProvider>
  );
}
