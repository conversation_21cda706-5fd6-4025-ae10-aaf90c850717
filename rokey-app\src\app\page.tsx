'use client';

import { useState, useEffect, Suspense, lazy } from 'react';
import { registerServiceWorker } from '@/utils/serviceWorker';
import LandingNavbar from '@/components/landing/LandingNavbar';
import HeroSection from '@/components/landing/HeroSection';
import TrustBadges from '@/components/landing/TrustBadges';

// Lazy load non-critical sections for better initial load performance
const FeaturesSection = lazy(() => import('@/components/landing/FeaturesSection'));
const RoutingVisualization = lazy(() => import('@/components/landing/RoutingVisualization'));
const TestimonialsSection = lazy(() => import('@/components/landing/TestimonialsSection'));
const CTASection = lazy(() => import('@/components/landing/CTASection'));
const Footer = lazy(() => import('@/components/landing/Footer'));

// Loading skeleton for lazy components
const SectionSkeleton = () => (
  <div className="py-20 animate-pulse">
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div className="h-8 bg-gray-200 rounded w-1/3 mx-auto mb-4"></div>
      <div className="h-4 bg-gray-200 rounded w-2/3 mx-auto mb-8"></div>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        {[1, 2, 3].map((i) => (
          <div key={i} className="h-48 bg-gray-200 rounded-lg"></div>
        ))}
      </div>
    </div>
  </div>
);

export default function LandingPage() {
  const [isLoaded, setIsLoaded] = useState(false);
  const [sectionsLoaded, setSectionsLoaded] = useState(false);

  useEffect(() => {
    // Register service worker for caching
    registerServiceWorker({
      onSuccess: () => console.log('✅ Service Worker registered for caching'),
      onUpdate: () => console.log('🔄 New content available'),
      onError: (error) => console.warn('⚠️ Service Worker registration failed:', error)
    });

    // Progressive loading: show critical content first
    setIsLoaded(true);

    // Load non-critical sections after a short delay
    const timer = setTimeout(() => {
      setSectionsLoaded(true);
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  // Prefetch likely next pages on hover
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const prefetchPages = () => {
        // Prefetch auth pages since users likely to sign up
        import('@/app/auth/signup/page');
        import('@/app/pricing/page');
        import('@/app/features/page');
      };

      // Prefetch after initial load
      const prefetchTimer = setTimeout(prefetchPages, 2000);
      return () => clearTimeout(prefetchTimer);
    }
  }, []);

  if (!isLoaded) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-blue-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#ff6b35] mx-auto mb-2"></div>
          <p className="text-gray-600 text-sm">Loading RouKey...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black">
      <LandingNavbar />

      <main>
        {/* Critical above-the-fold content - loads immediately */}
        <HeroSection />
        <TrustBadges />

        {/* Non-critical content - loads progressively */}
        {sectionsLoaded ? (
          <Suspense fallback={<SectionSkeleton />}>
            <RoutingVisualization />
          </Suspense>
        ) : (
          <SectionSkeleton />
        )}

        {sectionsLoaded ? (
          <Suspense fallback={<SectionSkeleton />}>
            <FeaturesSection />
          </Suspense>
        ) : (
          <SectionSkeleton />
        )}

        {sectionsLoaded ? (
          <Suspense fallback={<SectionSkeleton />}>
            <TestimonialsSection />
          </Suspense>
        ) : (
          <SectionSkeleton />
        )}

        {sectionsLoaded ? (
          <Suspense fallback={<SectionSkeleton />}>
            <CTASection />
          </Suspense>
        ) : (
          <SectionSkeleton />
        )}
      </main>

      {sectionsLoaded ? (
        <Suspense fallback={<div className="h-32 bg-gray-900"></div>}>
          <Footer />
        </Suspense>
      ) : (
        <div className="h-32 bg-gray-900"></div>
      )}
    </div>
  );
}
