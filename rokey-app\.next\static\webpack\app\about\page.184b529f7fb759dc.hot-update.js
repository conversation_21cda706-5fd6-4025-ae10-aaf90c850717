"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/about/page",{

/***/ "(app-pages-browser)/./src/components/OptimisticLink.tsx":
/*!*******************************************!*\
  !*** ./src/components/OptimisticLink.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OptimisticButton: () => (/* binding */ OptimisticButton),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useOptimisticNavigation: () => (/* binding */ useOptimisticNavigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/NavigationContext */ \"(app-pages-browser)/./src/contexts/NavigationContext.tsx\");\n/* harmony import */ var _utils_cacheStrategy__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/cacheStrategy */ \"(app-pages-browser)/./src/utils/cacheStrategy.ts\");\n/* __next_internal_client_entry_do_not_use__ default,OptimisticButton,useOptimisticNavigation auto */ \n\n\n\n\nconst OptimisticLink = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(_c = (param, ref)=>{\n    let { href, children, className = '', prefetch = true, onClick, onMouseEnter, ...props } = param;\n    // Use try-catch to handle cases where NavigationProvider might not be available\n    let navigationContext;\n    try {\n        navigationContext = (0,_contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_3__.useNavigation)();\n    } catch (error) {\n        // Fallback to regular Link behavior if NavigationProvider is not available\n        console.warn('NavigationProvider not found, falling back to regular Link behavior');\n        navigationContext = null;\n    }\n    const { navigateOptimistically, isNavigating, targetRoute } = navigationContext || {\n        navigateOptimistically: ()=>{},\n        isNavigating: false,\n        targetRoute: null\n    };\n    const handleClick = (e)=>{\n        // Don't prevent default for external links or special cases\n        if (href.startsWith('http') || href.startsWith('mailto:') || href.startsWith('tel:')) {\n            onClick === null || onClick === void 0 ? void 0 : onClick(e);\n            return;\n        }\n        // Don't prevent default if user is holding modifier keys\n        if (e.metaKey || e.ctrlKey || e.shiftKey || e.altKey) {\n            onClick === null || onClick === void 0 ? void 0 : onClick(e);\n            return;\n        }\n        // Prevent default navigation and use optimistic loading\n        e.preventDefault();\n        // Call custom onClick if provided\n        onClick === null || onClick === void 0 ? void 0 : onClick(e);\n        // Start optimistic navigation\n        navigateOptimistically(href);\n    };\n    const handleMouseEnter = (e)=>{\n        // Prefetch on hover for better performance\n        if (prefetch && href.startsWith('/')) {\n            _utils_cacheStrategy__WEBPACK_IMPORTED_MODULE_4__.prefetcher.schedulePrefetch(href);\n        }\n        // Call custom onMouseEnter if provided\n        onMouseEnter === null || onMouseEnter === void 0 ? void 0 : onMouseEnter(e);\n    };\n    // Add loading state to className if this link is being navigated to\n    const isCurrentlyNavigating = isNavigating && targetRoute === href;\n    const finalClassName = \"\".concat(className, \" \").concat(isCurrentlyNavigating ? 'optimistic-loading' : '').trim();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n        ref: ref,\n        href: href,\n        className: finalClassName,\n        onClick: handleClick,\n        onMouseEnter: handleMouseEnter,\n        prefetch: prefetch,\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticLink.tsx\",\n        lineNumber: 90,\n        columnNumber: 7\n    }, undefined);\n});\n_c1 = OptimisticLink;\nOptimisticLink.displayName = 'OptimisticLink';\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (OptimisticLink);\nconst OptimisticButton = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(_c2 = (param, ref)=>{\n    let { href, children, className = '', onClick, onMouseEnter, disabled = false, type = 'button', ...props } = param;\n    // Use try-catch to handle cases where NavigationProvider might not be available\n    let navigationContext;\n    try {\n        navigationContext = (0,_contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_3__.useNavigation)();\n    } catch (error) {\n        // Fallback to regular button behavior if NavigationProvider is not available\n        console.warn('NavigationProvider not found, falling back to regular button behavior');\n        navigationContext = null;\n    }\n    const { navigateOptimistically, isNavigating, targetRoute } = navigationContext || {\n        navigateOptimistically: ()=>{},\n        isNavigating: false,\n        targetRoute: null\n    };\n    const handleClick = (e)=>{\n        if (disabled) return;\n        // Call custom onClick first\n        onClick === null || onClick === void 0 ? void 0 : onClick(e);\n        // If href is provided, navigate optimistically\n        if (href && href.startsWith('/')) {\n            navigateOptimistically(href);\n        }\n    };\n    const handleMouseEnter = (e)=>{\n        // Prefetch on hover if href is provided\n        if (href && href.startsWith('/')) {\n            _utils_cacheStrategy__WEBPACK_IMPORTED_MODULE_4__.prefetcher.schedulePrefetch(href);\n        }\n        // Call custom onMouseEnter if provided\n        onMouseEnter === null || onMouseEnter === void 0 ? void 0 : onMouseEnter(e);\n    };\n    // Add loading state to className if this button's href is being navigated to\n    const isCurrentlyNavigating = isNavigating && targetRoute === href;\n    const finalClassName = \"\".concat(className, \" \").concat(isCurrentlyNavigating ? 'optimistic-loading' : '', \" \").concat(disabled ? 'opacity-50 cursor-not-allowed' : '').trim();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        ref: ref,\n        type: type,\n        className: finalClassName,\n        onClick: handleClick,\n        onMouseEnter: handleMouseEnter,\n        disabled: disabled,\n        ...props,\n        children: [\n            children,\n            isCurrentlyNavigating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"ml-2 inline-block w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticLink.tsx\",\n                lineNumber: 189,\n                columnNumber: 11\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticLink.tsx\",\n        lineNumber: 178,\n        columnNumber: 7\n    }, undefined);\n});\n_c3 = OptimisticButton;\nOptimisticButton.displayName = 'OptimisticButton';\n// Hook for programmatic optimistic navigation\nfunction useOptimisticNavigation() {\n    // Use try-catch to handle cases where NavigationProvider might not be available\n    let navigationContext;\n    try {\n        navigationContext = (0,_contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_3__.useNavigation)();\n    } catch (error) {\n        // Fallback behavior if NavigationProvider is not available\n        console.warn('NavigationProvider not found, falling back to regular navigation behavior');\n        navigationContext = null;\n    }\n    const { navigateOptimistically, isNavigating, targetRoute, isPageCached } = navigationContext || {\n        navigateOptimistically: ()=>{},\n        isNavigating: false,\n        targetRoute: null,\n        isPageCached: ()=>false\n    };\n    const navigate = (href)=>{\n        if (href.startsWith('/')) {\n            navigateOptimistically(href);\n        } else {\n            // For external links, use regular navigation\n            window.location.href = href;\n        }\n    };\n    const isNavigatingTo = (href)=>{\n        return isNavigating && targetRoute === href;\n    };\n    const isCached = (href)=>{\n        return isPageCached(href);\n    };\n    return {\n        navigate,\n        isNavigating,\n        targetRoute,\n        isNavigatingTo,\n        isCached\n    };\n}\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"OptimisticLink$forwardRef\");\n$RefreshReg$(_c1, \"OptimisticLink\");\n$RefreshReg$(_c2, \"OptimisticButton$forwardRef\");\n$RefreshReg$(_c3, \"OptimisticButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/OptimisticLink.tsx\n"));

/***/ })

});