import bundleAnalyzer from '@next/bundle-analyzer';

const withBundleAnalyzer = bundleAnalyzer({
  enabled: process.env.ANALYZE === 'true',
});

/** @type {import('next').NextConfig} */
const nextConfig = {
  webpack: (config, { isServer, dev }) => {
    // Ignore test files during build
    config.module.rules.push({
      test: /node_modules\/pdf-parse\/test\//,
      use: 'ignore-loader'
    });

    // Development-specific optimizations
    if (dev) {
      // Faster development builds
      config.optimization.removeAvailableModules = false;
      config.optimization.removeEmptyChunks = false;
      config.optimization.splitChunks = false;

      // Reduce bundle analysis overhead in development
      config.stats = 'errors-warnings';

      // Use default Next.js devtool for development
      // config.devtool = 'eval-cheap-module-source-map';

      // Allow default Next.js caching in development
      // config.cache = false;
    }

    // Production optimizations
    if (!dev && !isServer) {
      // Enhanced chunk splitting for better caching and loading
      config.optimization.splitChunks = {
        chunks: 'all',
        minSize: 20000,
        maxSize: 244000,
        cacheGroups: {
          // Critical UI components - load immediately
          critical: {
            test: /[\\/]node_modules[\\/](@heroicons\/react\/24\/(outline|solid))[\\/]/,
            name: 'critical-ui',
            chunks: 'all',
            priority: 50,
            enforce: true,
          },
          // Heavy markdown libraries - lazy load
          markdown: {
            test: /[\\/]node_modules[\\/](react-markdown|react-syntax-highlighter|remark-gfm|prismjs|highlight\.js|lowlight|refractor|rehype-highlight)[\\/]/,
            name: 'markdown',
            chunks: 'async',
            priority: 40,
            enforce: true,
          },
          // Supabase and auth libraries
          supabase: {
            test: /[\\/]node_modules[\\/](@supabase)[\\/]/,
            name: 'supabase',
            chunks: 'all',
            priority: 35,
          },
          // OpenAI and AI libraries
          ai: {
            test: /[\\/]node_modules[\\/](openai|@google-ai|@google\/generative-ai|google-auth-library)[\\/]/,
            name: 'ai-libs',
            chunks: 'async',
            priority: 30,
          },
          // UI libraries
          ui: {
            test: /[\\/]node_modules[\\/](@headlessui|react-tooltip|@dnd-kit)[\\/]/,
            name: 'ui-libs',
            chunks: 'all',
            priority: 25,
          },
          // React core
          react: {
            test: /[\\/]node_modules[\\/](react|react-dom)[\\/]/,
            name: 'react',
            chunks: 'all',
            priority: 20,
          },
          // UI components chunk
          uiComponents: {
            test: /[\\/]src[\\/]components[\\/]/,
            name: 'ui-components',
            chunks: 'all',
            priority: 15,
          },
          // Performance monitoring - separate chunk
          performance: {
            test: /[\\/]src[\\/](hooks|utils)[\\/](usePerformanceOptimization|cacheStrategy|advancedCache)\.ts$/,
            name: 'performance',
            chunks: 'async',
            priority: 30,
            enforce: true,
          },
          // Landing page components - high priority
          landing: {
            test: /[\\/]src[\\/]components[\\/]landing[\\/]/,
            name: 'landing-components',
            chunks: 'all',
            priority: 35,
            enforce: true,
          },
          // Utils chunk
          utils: {
            test: /[\\/]src[\\/](utils|hooks|lib)[\\/]/,
            name: 'utils',
            chunks: 'all',
            priority: 15,
          },
          // Other vendor libraries
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all',
            priority: 10,
            minChunks: 2,
          },
          // Common chunk for shared code
          common: {
            name: 'common',
            minChunks: 2,
            chunks: 'all',
            priority: 1,
          },
        },
      };

      // Tree shaking improvements
      config.optimization.usedExports = true;
      config.optimization.sideEffects = false;
      config.optimization.providedExports = true;
      config.optimization.concatenateModules = true;

      // Module resolution optimizations
      config.resolve.alias = {
        ...config.resolve.alias,
        // Optimize React imports
        'react': 'react/index.js',
        'react-dom': 'react-dom/index.js',
      };
    }

    return config;
  },
  // External packages for server components
  serverExternalPackages: ['pdf-parse', 'mammoth'],

  // Turbopack configuration (moved from experimental)
  turbopack: {
    rules: {
      '*.svg': {
        loaders: ['@svgr/webpack'],
        as: '*.js',
      },
    },
  },

  // External packages for server components (moved from experimental)
  serverExternalPackages: ['pdf-parse', 'mammoth'],

  // Turbopack configuration (moved from experimental.turbo)
  turbopack: {
    rules: {
      '*.svg': {
        loaders: ['@svgr/webpack'],
        as: '*.js',
      },
    },
  },

  experimental: {
    // Phase 2B: Enhanced package import optimization
    optimizePackageImports: [
      '@heroicons/react',
      '@headlessui/react',
      'react-markdown',
      'react-syntax-highlighter',
      '@supabase/supabase-js'
    ],
    // Phase 2B: Enable modern bundling features
    esmExternals: true,
    scrollRestoration: true,
    // Speed up development builds
    forceSwcTransforms: true,
  },
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
    // Enable React compiler optimizations
    reactRemoveProperties: process.env.NODE_ENV === 'production',
  },
  // Enable static optimization
  trailingSlash: false,
  // Optimize images
  images: {
    formats: ['image/webp', 'image/avif'],
    minimumCacheTTL: 60,
    dangerouslyAllowSVG: true,
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },
  // Enable compression
  compress: true,
  // Configure redirects - Removed dashboard redirect for new landing page
  async redirects() {
    return [];
  },
  // Optimize headers for caching
  async headers() {
    return [
      {
        source: '/api/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=30, stale-while-revalidate=60',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
        ],
      },
      {
        source: '/_next/static/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
      {
        source: '/manifest.json',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=86400',
          },
        ],
      },
      {
        source: '/sw.js',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=0, must-revalidate',
          },
          {
            key: 'Service-Worker-Allowed',
            value: '/',
          },
        ],
      },
      {
        source: '/',
        headers: [
          {
            key: 'Cache-Control',
            value: 'no-cache, no-store, must-revalidate',
          },
          {
            key: 'Pragma',
            value: 'no-cache',
          },
          {
            key: 'Expires',
            value: '0',
          },
        ],
      },
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-DNS-Prefetch-Control',
            value: 'on',
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
        ],
      },
    ];
  },
};

export default withBundleAnalyzer(nextConfig);