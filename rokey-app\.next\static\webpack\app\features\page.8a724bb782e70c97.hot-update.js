"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/features/page",{

/***/ "(app-pages-browser)/./src/app/features/page.tsx":
/*!***********************************!*\
  !*** ./src/app/features/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FeaturesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,CloudIcon,CodeBracketIcon,CogIcon,CpuChipIcon,LightBulbIcon,RocketLaunchIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,CloudIcon,CodeBracketIcon,CogIcon,CpuChipIcon,LightBulbIcon,RocketLaunchIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,CloudIcon,CodeBracketIcon,CogIcon,CpuChipIcon,LightBulbIcon,RocketLaunchIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,CloudIcon,CodeBracketIcon,CogIcon,CpuChipIcon,LightBulbIcon,RocketLaunchIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,CloudIcon,CodeBracketIcon,CogIcon,CpuChipIcon,LightBulbIcon,RocketLaunchIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowPathIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,CloudIcon,CodeBracketIcon,CogIcon,CpuChipIcon,LightBulbIcon,RocketLaunchIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CloudIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,CloudIcon,CodeBracketIcon,CogIcon,CpuChipIcon,LightBulbIcon,RocketLaunchIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CodeBracketIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,CloudIcon,CodeBracketIcon,CogIcon,CpuChipIcon,LightBulbIcon,RocketLaunchIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/LightBulbIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,CloudIcon,CodeBracketIcon,CogIcon,CpuChipIcon,LightBulbIcon,RocketLaunchIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/RocketLaunchIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,CloudIcon,CodeBracketIcon,CogIcon,CpuChipIcon,LightBulbIcon,RocketLaunchIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CogIcon.js\");\n/* harmony import */ var _components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/landing/LandingNavbar */ \"(app-pages-browser)/./src/components/landing/LandingNavbar.tsx\");\n/* harmony import */ var _hooks_usePerformanceOptimization__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/usePerformanceOptimization */ \"(app-pages-browser)/./src/hooks/usePerformanceOptimization.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n// Lazy load footer for better initial load\nconst Footer = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.lazy)(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_landing_Footer_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/landing/Footer */ \"(app-pages-browser)/./src/components/landing/Footer.tsx\")));\n_c = Footer;\nconst features = [\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: \"Intelligent Routing\",\n        description: \"Advanced AI algorithms analyze your prompts and automatically route them to the optimal model for best results.\",\n        details: [\n            \"Real-time prompt analysis\",\n            \"Context-aware routing decisions\",\n            \"Performance optimization\",\n            \"Cost-effective model selection\"\n        ]\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        title: \"Enterprise Security\",\n        description: \"Bank-grade security with end-to-end encryption, compliance certifications, and audit trails.\",\n        details: [\n            \"SOC 2 Type II compliance\",\n            \"End-to-end encryption\",\n            \"Complete audit trails\",\n            \"Role-based access control\"\n        ]\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        title: \"Advanced Analytics\",\n        description: \"Comprehensive insights into model performance, costs, and usage patterns with real-time dashboards.\",\n        details: [\n            \"Real-time performance metrics\",\n            \"Cost optimization insights\",\n            \"Usage pattern analysis\",\n            \"Custom reporting\"\n        ]\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        title: \"300+ AI Models\",\n        description: \"Access to the largest collection of AI models from leading providers, all through one unified API.\",\n        details: [\n            \"OpenAI, Anthropic, Google, Meta\",\n            \"Specialized domain models\",\n            \"Latest model versions\",\n            \"Custom model integration\"\n        ]\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        title: \"Auto-Failover\",\n        description: \"Automatic failover and retry mechanisms ensure 99.9% uptime for your AI applications.\",\n        details: [\n            \"Intelligent failover routing\",\n            \"Automatic retry logic\",\n            \"Health monitoring\",\n            \"Zero-downtime switching\"\n        ]\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        title: \"Global Infrastructure\",\n        description: \"Distributed infrastructure across multiple regions for low latency and high availability.\",\n        details: [\n            \"Multi-region deployment\",\n            \"Edge computing optimization\",\n            \"CDN acceleration\",\n            \"99.9% uptime SLA\"\n        ]\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        title: \"Developer-First\",\n        description: \"Built by developers, for developers. Simple APIs, comprehensive docs, and powerful SDKs.\",\n        details: [\n            \"RESTful API design\",\n            \"Multiple SDK languages\",\n            \"Interactive documentation\",\n            \"Code examples & tutorials\"\n        ]\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        title: \"Smart Optimization\",\n        description: \"Continuous learning algorithms optimize routing decisions based on your usage patterns.\",\n        details: [\n            \"Machine learning optimization\",\n            \"Pattern recognition\",\n            \"Adaptive routing\",\n            \"Performance tuning\"\n        ]\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        title: \"Rapid Deployment\",\n        description: \"Get started in minutes with our simple integration process and migration tools.\",\n        details: [\n            \"5-minute setup\",\n            \"Migration assistance\",\n            \"Zero-code integration\",\n            \"Instant activation\"\n        ]\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        title: \"Custom Configurations\",\n        description: \"Flexible routing rules and custom configurations to match your specific requirements.\",\n        details: [\n            \"Custom routing logic\",\n            \"Business rule engine\",\n            \"A/B testing support\",\n            \"Configuration templates\"\n        ]\n    }\n];\nfunction FeaturesPage() {\n    _s();\n    const { startMeasurement, endMeasurement } = (0,_hooks_usePerformanceOptimization__WEBPACK_IMPORTED_MODULE_3__.usePerformanceOptimization)('FeaturesPage', {\n        enableMonitoring: true,\n        enableMemoryTracking: true,\n        warningThresholds: {\n            renderTime: 200,\n            memoryUsage: 40 * 1024 * 1024 // 40MB\n        }\n    });\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"FeaturesPage.useEffect\": ()=>{\n            startMeasurement();\n            return ({\n                \"FeaturesPage.useEffect\": ()=>endMeasurement()\n            })[\"FeaturesPage.useEffect\"];\n        }\n    }[\"FeaturesPage.useEffect\"], [\n        startMeasurement,\n        endMeasurement\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                lineNumber: 153,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"pt-20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20 bg-gradient-to-br from-slate-50 to-blue-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-5xl md:text-6xl font-bold text-gray-900 mb-6\",\n                                        children: [\n                                            \"Powerful Features for\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-[#ff6b35] block\",\n                                                children: \"Modern AI Development\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-gray-600 max-w-3xl mx-auto mb-8\",\n                                        children: \"Everything you need to build, deploy, and scale AI applications with confidence. From intelligent routing to enterprise security, we've got you covered.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-12\",\n                                children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.6,\n                                            delay: index * 0.1\n                                        },\n                                        className: \"bg-white rounded-2xl p-8 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-12 h-12 bg-[#ff6b35] rounded-xl flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                            className: \"w-6 h-6 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                                            lineNumber: 191,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                                        lineNumber: 190,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                                    lineNumber: 189,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-semibold text-gray-900 mb-3\",\n                                                            children: feature.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                                            lineNumber: 195,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 mb-4\",\n                                                            children: feature.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                                            lineNumber: 198,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"space-y-2\",\n                                                            children: feature.details.map((detail, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    className: \"flex items-center text-sm text-gray-500\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-1.5 h-1.5 bg-[#ff6b35] rounded-full mr-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                                                            lineNumber: 204,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        detail\n                                                                    ]\n                                                                }, idx, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                                                    lineNumber: 203,\n                                                                    columnNumber: 27\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                                            lineNumber: 201,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, feature.title, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20 bg-gray-900\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-4xl font-bold text-white mb-6\",\n                                        children: \"Ready to Transform Your AI Development?\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-gray-300 mb-8\",\n                                        children: \"Join thousands of developers who trust RouKey for their AI infrastructure.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.button, {\n                                        whileHover: {\n                                            scale: 1.05\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        className: \"bg-[#ff6b35] text-white px-8 py-4 rounded-xl font-semibold text-lg hover:bg-[#e55a2b] transition-colors duration-200 shadow-lg\",\n                                        children: \"Get Started Now\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                lineNumber: 155,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Footer, {}, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                lineNumber: 243,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n        lineNumber: 152,\n        columnNumber: 5\n    }, this);\n}\n_s(FeaturesPage, \"Q+OT+PfUl/epZO1+DioOG7h1YRE=\", false, function() {\n    return [\n        _hooks_usePerformanceOptimization__WEBPACK_IMPORTED_MODULE_3__.usePerformanceOptimization\n    ];\n});\n_c1 = FeaturesPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"Footer\");\n$RefreshReg$(_c1, \"FeaturesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/features/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/usePerformanceOptimization.ts":
/*!*************************************************!*\
  !*** ./src/hooks/usePerformanceOptimization.ts ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   usePageLoadPerformance: () => (/* binding */ usePageLoadPerformance),\n/* harmony export */   usePerformanceOptimization: () => (/* binding */ usePerformanceOptimization)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ usePerformanceOptimization,usePageLoadPerformance auto */ \nfunction usePerformanceOptimization(componentName) {\n    let config = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const { enableMonitoring = true, enableMemoryTracking = true, enableBundleAnalysis = false, enableCacheTracking = true, warningThresholds = {\n        renderTime: 100,\n        memoryUsage: 50 * 1024 * 1024,\n        bundleSize: 1024 * 1024 // 1MB\n    } } = config;\n    const [metrics, setMetrics] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        renderTime: 0\n    });\n    const renderStartTime = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    const cacheHits = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    const cacheRequests = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    // Start performance measurement\n    const startMeasurement = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"usePerformanceOptimization.useCallback[startMeasurement]\": ()=>{\n            if (!enableMonitoring) return;\n            renderStartTime.current = performance.now();\n        }\n    }[\"usePerformanceOptimization.useCallback[startMeasurement]\"], [\n        enableMonitoring\n    ]);\n    // End performance measurement\n    const endMeasurement = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"usePerformanceOptimization.useCallback[endMeasurement]\": ()=>{\n            if (!enableMonitoring || !renderStartTime.current) return;\n            const renderTime = performance.now() - renderStartTime.current;\n            setMetrics({\n                \"usePerformanceOptimization.useCallback[endMeasurement]\": (prev)=>({\n                        ...prev,\n                        renderTime\n                    })\n            }[\"usePerformanceOptimization.useCallback[endMeasurement]\"]);\n            // Log warnings if thresholds exceeded\n            if (renderTime > (warningThresholds.renderTime || 100)) {\n                console.warn(\"⚠️ \".concat(componentName, \" slow render: \").concat(renderTime.toFixed(2), \"ms\"));\n            } else if (renderTime < 16) {\n                console.log(\"✅ \".concat(componentName, \" fast render: \").concat(renderTime.toFixed(2), \"ms\"));\n            }\n            renderStartTime.current = 0;\n        }\n    }[\"usePerformanceOptimization.useCallback[endMeasurement]\"], [\n        componentName,\n        enableMonitoring,\n        warningThresholds.renderTime\n    ]);\n    // Memory usage tracking\n    const trackMemoryUsage = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"usePerformanceOptimization.useCallback[trackMemoryUsage]\": ()=>{\n            if (!enableMemoryTracking || !('memory' in performance)) return;\n            const memory = performance.memory;\n            const memoryUsage = {\n                used: memory.usedJSHeapSize,\n                total: memory.totalJSHeapSize,\n                limit: memory.jsHeapSizeLimit\n            };\n            setMetrics({\n                \"usePerformanceOptimization.useCallback[trackMemoryUsage]\": (prev)=>({\n                        ...prev,\n                        memoryUsage\n                    })\n            }[\"usePerformanceOptimization.useCallback[trackMemoryUsage]\"]);\n            // Warning for high memory usage\n            if (memoryUsage.used > (warningThresholds.memoryUsage || 50 * 1024 * 1024)) {\n                console.warn(\"⚠️ High memory usage in \".concat(componentName, \": \").concat((memoryUsage.used / 1024 / 1024).toFixed(2), \"MB\"));\n            }\n        }\n    }[\"usePerformanceOptimization.useCallback[trackMemoryUsage]\"], [\n        componentName,\n        enableMemoryTracking,\n        warningThresholds.memoryUsage\n    ]);\n    // Bundle size analysis\n    const analyzeBundleSize = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"usePerformanceOptimization.useCallback[analyzeBundleSize]\": ()=>{\n            if (!enableBundleAnalysis) return;\n            const resources = performance.getEntriesByType('resource');\n            let totalJSSize = 0;\n            resources.forEach({\n                \"usePerformanceOptimization.useCallback[analyzeBundleSize]\": (resource)=>{\n                    if (resource.name.includes('.js') && resource.transferSize) {\n                        totalJSSize += resource.transferSize;\n                    }\n                }\n            }[\"usePerformanceOptimization.useCallback[analyzeBundleSize]\"]);\n            setMetrics({\n                \"usePerformanceOptimization.useCallback[analyzeBundleSize]\": (prev)=>({\n                        ...prev,\n                        bundleSize: totalJSSize\n                    })\n            }[\"usePerformanceOptimization.useCallback[analyzeBundleSize]\"]);\n            if (totalJSSize > (warningThresholds.bundleSize || 1024 * 1024)) {\n                console.warn(\"⚠️ Large bundle size: \".concat((totalJSSize / 1024 / 1024).toFixed(2), \"MB\"));\n            }\n        }\n    }[\"usePerformanceOptimization.useCallback[analyzeBundleSize]\"], [\n        enableBundleAnalysis,\n        warningThresholds.bundleSize\n    ]);\n    // Cache hit rate tracking\n    const trackCacheHitRate = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"usePerformanceOptimization.useCallback[trackCacheHitRate]\": ()=>{\n            if (!enableCacheTracking) return;\n            const hitRate = cacheRequests.current > 0 ? cacheHits.current / cacheRequests.current * 100 : 0;\n            setMetrics({\n                \"usePerformanceOptimization.useCallback[trackCacheHitRate]\": (prev)=>({\n                        ...prev,\n                        cacheHitRate: hitRate\n                    })\n            }[\"usePerformanceOptimization.useCallback[trackCacheHitRate]\"]);\n        }\n    }[\"usePerformanceOptimization.useCallback[trackCacheHitRate]\"], [\n        enableCacheTracking\n    ]);\n    // Service Worker message handler for cache events\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"usePerformanceOptimization.useEffect\": ()=>{\n            if (!enableCacheTracking || \"object\" === 'undefined') return;\n            const handleSWMessage = {\n                \"usePerformanceOptimization.useEffect.handleSWMessage\": (event)=>{\n                    var _event_data, _event_data1;\n                    if (((_event_data = event.data) === null || _event_data === void 0 ? void 0 : _event_data.type) === 'CACHE_HIT') {\n                        cacheHits.current++;\n                        cacheRequests.current++;\n                        trackCacheHitRate();\n                    } else if (((_event_data1 = event.data) === null || _event_data1 === void 0 ? void 0 : _event_data1.type) === 'CACHE_MISS') {\n                        cacheRequests.current++;\n                        trackCacheHitRate();\n                    }\n                }\n            }[\"usePerformanceOptimization.useEffect.handleSWMessage\"];\n            if ('serviceWorker' in navigator) {\n                navigator.serviceWorker.addEventListener('message', handleSWMessage);\n                return ({\n                    \"usePerformanceOptimization.useEffect\": ()=>{\n                        navigator.serviceWorker.removeEventListener('message', handleSWMessage);\n                    }\n                })[\"usePerformanceOptimization.useEffect\"];\n            }\n        }\n    }[\"usePerformanceOptimization.useEffect\"], [\n        enableCacheTracking,\n        trackCacheHitRate\n    ]);\n    // Navigation timing\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"usePerformanceOptimization.useEffect\": ()=>{\n            if (!enableMonitoring) return;\n            const measureNavigation = {\n                \"usePerformanceOptimization.useEffect.measureNavigation\": ()=>{\n                    const navigation = performance.getEntriesByType('navigation')[0];\n                    if (navigation) {\n                        const navigationTime = navigation.loadEventEnd - navigation.navigationStart;\n                        setMetrics({\n                            \"usePerformanceOptimization.useEffect.measureNavigation\": (prev)=>({\n                                    ...prev,\n                                    navigationTime\n                                })\n                        }[\"usePerformanceOptimization.useEffect.measureNavigation\"]);\n                    }\n                }\n            }[\"usePerformanceOptimization.useEffect.measureNavigation\"];\n            // Measure after page load\n            if (document.readyState === 'complete') {\n                measureNavigation();\n            } else {\n                window.addEventListener('load', measureNavigation);\n                return ({\n                    \"usePerformanceOptimization.useEffect\": ()=>window.removeEventListener('load', measureNavigation)\n                })[\"usePerformanceOptimization.useEffect\"];\n            }\n        }\n    }[\"usePerformanceOptimization.useEffect\"], [\n        enableMonitoring\n    ]);\n    // Periodic monitoring\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"usePerformanceOptimization.useEffect\": ()=>{\n            if (!enableMonitoring) return;\n            const interval = setInterval({\n                \"usePerformanceOptimization.useEffect.interval\": ()=>{\n                    trackMemoryUsage();\n                    analyzeBundleSize();\n                }\n            }[\"usePerformanceOptimization.useEffect.interval\"], 5000); // Check every 5 seconds\n            return ({\n                \"usePerformanceOptimization.useEffect\": ()=>clearInterval(interval)\n            })[\"usePerformanceOptimization.useEffect\"];\n        }\n    }[\"usePerformanceOptimization.useEffect\"], [\n        enableMonitoring,\n        trackMemoryUsage,\n        analyzeBundleSize\n    ]);\n    // Performance optimization suggestions\n    const getOptimizationSuggestions = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"usePerformanceOptimization.useCallback[getOptimizationSuggestions]\": ()=>{\n            const suggestions = [];\n            if (metrics.renderTime > 100) {\n                suggestions.push('Consider memoizing expensive calculations');\n                suggestions.push('Use React.memo for component optimization');\n                suggestions.push('Implement virtualization for large lists');\n            }\n            if (metrics.memoryUsage && metrics.memoryUsage.used > 50 * 1024 * 1024) {\n                suggestions.push('Check for memory leaks');\n                suggestions.push('Optimize image sizes and formats');\n                suggestions.push('Implement proper cleanup in useEffect');\n            }\n            if (metrics.bundleSize && metrics.bundleSize > 1024 * 1024) {\n                suggestions.push('Implement code splitting');\n                suggestions.push('Use dynamic imports for heavy components');\n                suggestions.push('Remove unused dependencies');\n            }\n            if (metrics.cacheHitRate !== undefined && metrics.cacheHitRate < 70) {\n                suggestions.push('Improve caching strategy');\n                suggestions.push('Implement service worker caching');\n                suggestions.push('Use browser cache headers');\n            }\n            return suggestions;\n        }\n    }[\"usePerformanceOptimization.useCallback[getOptimizationSuggestions]\"], [\n        metrics\n    ]);\n    // Export performance data for debugging\n    const exportMetrics = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"usePerformanceOptimization.useCallback[exportMetrics]\": ()=>{\n            const exportData = {\n                component: componentName,\n                timestamp: new Date().toISOString(),\n                metrics,\n                suggestions: getOptimizationSuggestions(),\n                userAgent: navigator.userAgent,\n                url: window.location.href\n            };\n            console.group(\"\\uD83D\\uDCCA Performance Report: \".concat(componentName));\n            console.table(metrics);\n            console.log('Suggestions:', getOptimizationSuggestions());\n            console.groupEnd();\n            return exportData;\n        }\n    }[\"usePerformanceOptimization.useCallback[exportMetrics]\"], [\n        componentName,\n        metrics,\n        getOptimizationSuggestions\n    ]);\n    return {\n        metrics,\n        startMeasurement,\n        endMeasurement,\n        trackMemoryUsage,\n        analyzeBundleSize,\n        trackCacheHitRate,\n        getOptimizationSuggestions,\n        exportMetrics\n    };\n}\n// Hook for monitoring page load performance\nfunction usePageLoadPerformance() {\n    const [loadMetrics, setLoadMetrics] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({});\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"usePageLoadPerformance.useEffect\": ()=>{\n            const measurePageLoad = {\n                \"usePageLoadPerformance.useEffect.measurePageLoad\": ()=>{\n                    const navigation = performance.getEntriesByType('navigation')[0];\n                    const paint = performance.getEntriesByType('paint');\n                    const metrics = {};\n                    if (navigation) {\n                        metrics.domContentLoaded = navigation.domContentLoadedEventEnd - navigation.navigationStart;\n                    }\n                    paint.forEach({\n                        \"usePageLoadPerformance.useEffect.measurePageLoad\": (entry)=>{\n                            if (entry.name === 'first-paint') {\n                                metrics.firstPaint = entry.startTime;\n                            } else if (entry.name === 'first-contentful-paint') {\n                                metrics.firstContentfulPaint = entry.startTime;\n                            }\n                        }\n                    }[\"usePageLoadPerformance.useEffect.measurePageLoad\"]);\n                    // Largest Contentful Paint\n                    if ('PerformanceObserver' in window) {\n                        const observer = new PerformanceObserver({\n                            \"usePageLoadPerformance.useEffect.measurePageLoad\": (list)=>{\n                                const entries = list.getEntries();\n                                const lastEntry = entries[entries.length - 1];\n                                metrics.largestContentfulPaint = lastEntry.startTime;\n                                setLoadMetrics({\n                                    \"usePageLoadPerformance.useEffect.measurePageLoad\": (prev)=>({\n                                            ...prev,\n                                            ...metrics\n                                        })\n                                }[\"usePageLoadPerformance.useEffect.measurePageLoad\"]);\n                            }\n                        }[\"usePageLoadPerformance.useEffect.measurePageLoad\"]);\n                        observer.observe({\n                            entryTypes: [\n                                'largest-contentful-paint'\n                            ]\n                        });\n                    }\n                    setLoadMetrics({\n                        \"usePageLoadPerformance.useEffect.measurePageLoad\": (prev)=>({\n                                ...prev,\n                                ...metrics\n                            })\n                    }[\"usePageLoadPerformance.useEffect.measurePageLoad\"]);\n                }\n            }[\"usePageLoadPerformance.useEffect.measurePageLoad\"];\n            if (document.readyState === 'complete') {\n                measurePageLoad();\n            } else {\n                window.addEventListener('load', measurePageLoad);\n                return ({\n                    \"usePageLoadPerformance.useEffect\": ()=>window.removeEventListener('load', measurePageLoad)\n                })[\"usePageLoadPerformance.useEffect\"];\n            }\n        }\n    }[\"usePageLoadPerformance.useEffect\"], []);\n    return loadMetrics;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/usePerformanceOptimization.ts\n"));

/***/ })

});