"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"c55833828dcf\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcUm9LZXkgQXBwXFxyb2tleS1hcHBcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImM1NTgzMzgyOGRjZlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/LayoutContent.tsx":
/*!******************************************!*\
  !*** ./src/components/LayoutContent.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LayoutContent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_Navbar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Navbar */ \"(app-pages-browser)/./src/components/Navbar.tsx\");\n/* harmony import */ var _components_Sidebar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Sidebar */ \"(app-pages-browser)/./src/components/Sidebar.tsx\");\n/* harmony import */ var _contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/SidebarContext */ \"(app-pages-browser)/./src/contexts/SidebarContext.tsx\");\n/* harmony import */ var _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/NavigationContext */ \"(app-pages-browser)/./src/contexts/NavigationContext.tsx\");\n/* harmony import */ var _components_OptimisticPageLoader__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/OptimisticPageLoader */ \"(app-pages-browser)/./src/components/OptimisticPageLoader.tsx\");\n/* harmony import */ var _hooks_useAdvancedPreloading__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useAdvancedPreloading */ \"(app-pages-browser)/./src/hooks/useAdvancedPreloading.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction LayoutContent(param) {\n    let { children } = param;\n    _s();\n    const { isCollapsed, collapseSidebar } = (0,_contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_3__.useSidebar)();\n    const { isNavigating, targetRoute, isPageCached } = (0,_contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_4__.useNavigation)();\n    // Initialize advanced preloading system\n    (0,_hooks_useAdvancedPreloading__WEBPACK_IMPORTED_MODULE_6__.useAdvancedPreloading)({\n        maxConcurrent: 2,\n        idleTimeout: 1500,\n        backgroundDelay: 3000\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-1 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden lg:block\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Sidebar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:hidden fixed inset-0 z-50 \".concat(isCollapsed ? 'pointer-events-none' : ''),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        onClick: collapseSidebar,\n                        className: \"absolute inset-0 bg-black transition-opacity duration-200 ease-out cursor-pointer \".concat(isCollapsed ? 'opacity-0' : 'opacity-50')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute left-0 top-0 h-full transform transition-transform duration-200 ease-out \".concat(isCollapsed ? '-translate-x-full' : 'translate-x-0'),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Sidebar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col overflow-hidden min-w-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 overflow-y-auto content-area\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 sm:p-6 lg:p-8 max-w-7xl mx-auto w-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"page-transition\",\n                                children: isNavigating && targetRoute ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_OptimisticPageLoader__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    targetRoute: targetRoute,\n                                    children: children\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 17\n                                }, this) : children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LayoutContent.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n_s(LayoutContent, \"pw3JeVKm4TvDQ+iCTK8pMVfflCY=\", false, function() {\n    return [\n        _contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_3__.useSidebar,\n        _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_4__.useNavigation,\n        _hooks_useAdvancedPreloading__WEBPACK_IMPORTED_MODULE_6__.useAdvancedPreloading\n    ];\n});\n_c = LayoutContent;\nvar _c;\n$RefreshReg$(_c, \"LayoutContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/LayoutContent.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/OptimisticPageLoader.tsx":
/*!*************************************************!*\
  !*** ./src/components/OptimisticPageLoader.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ OptimisticPageLoader),\n/* harmony export */   useOptimisticNavigation: () => (/* binding */ useOptimisticNavigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/NavigationContext */ \"(app-pages-browser)/./src/contexts/NavigationContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default,useOptimisticNavigation auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n// Page skeleton components for instant loading\nconst DashboardSkeleton = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6 animate-pulse\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-8 bg-gray-200 rounded w-1/3\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                lineNumber: 10,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                children: [\n                    1,\n                    2,\n                    3\n                ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white p-6 rounded-lg border\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-4 bg-gray-200 rounded w-1/2 mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                                lineNumber: 14,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-8 bg-gray-200 rounded w-1/4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                                lineNumber: 15,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, i, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                lineNumber: 11,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-6 rounded-lg border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-6 bg-gray-200 rounded w-1/4 mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            1,\n                            2,\n                            3,\n                            4\n                        ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-4 bg-gray-200 rounded\"\n                            }, i, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                                lineNumber: 23,\n                                columnNumber: 11\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                lineNumber: 19,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined);\n_c = DashboardSkeleton;\nconst PricingSkeleton = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8 animate-pulse\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-12 bg-gray-200 rounded w-1/2 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-6 bg-gray-200 rounded w-3/4 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                lineNumber: 32,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                children: [\n                    1,\n                    2,\n                    3\n                ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white p-8 rounded-2xl border-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-6 bg-gray-200 rounded w-1/2 mx-auto mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                                        lineNumber: 40,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-gray-200 rounded w-3/4 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-10 bg-gray-200 rounded w-1/3 mx-auto\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                                        lineNumber: 42,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3 mb-8\",\n                                children: [\n                                    1,\n                                    2,\n                                    3,\n                                    4,\n                                    5\n                                ].map((j)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-gray-200 rounded\"\n                                    }, j, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                                lineNumber: 44,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-12 bg-gray-200 rounded\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, i, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                lineNumber: 36,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n        lineNumber: 31,\n        columnNumber: 3\n    }, undefined);\n_c1 = PricingSkeleton;\nconst FeaturesSkeleton = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8 animate-pulse\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-20 bg-gradient-to-br from-slate-50 to-blue-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-16 bg-gray-200 rounded w-2/3 mx-auto mb-6\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-6 bg-gray-200 rounded w-3/4 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                lineNumber: 58,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-12\",\n                    children: [\n                        1,\n                        2,\n                        3,\n                        4,\n                        5,\n                        6\n                    ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-2xl p-8 border\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-gray-200 rounded-xl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                                        lineNumber: 67,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-6 bg-gray-200 rounded w-1/2 mb-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                                                lineNumber: 69,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-4 bg-gray-200 rounded mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    1,\n                                                    2,\n                                                    3\n                                                ].map((j)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-3 bg-gray-200 rounded w-3/4\"\n                                                    }, j, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                                                        lineNumber: 73,\n                                                        columnNumber: 21\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                                                lineNumber: 71,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 13\n                            }, undefined)\n                        }, i, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                lineNumber: 62,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n        lineNumber: 57,\n        columnNumber: 3\n    }, undefined);\n_c2 = FeaturesSkeleton;\nconst AuthSkeleton = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-blue-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white p-8 rounded-2xl shadow-lg border w-full max-w-md animate-pulse\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-8 bg-gray-200 rounded w-1/2 mx-auto mb-2\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-4 bg-gray-200 rounded w-3/4 mx-auto\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                    lineNumber: 88,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-12 bg-gray-200 rounded\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-12 bg-gray-200 rounded\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-12 bg-gray-200 rounded\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-12 bg-gray-200 rounded\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n            lineNumber: 87,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n        lineNumber: 86,\n        columnNumber: 3\n    }, undefined);\n_c3 = AuthSkeleton;\nconst PlaygroundSkeleton = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6 animate-pulse\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-8 bg-gray-200 rounded w-1/4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                lineNumber: 104,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white p-6 rounded-lg border\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-6 bg-gray-200 rounded w-1/3 mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    1,\n                                    2,\n                                    3\n                                ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-gray-200 rounded\"\n                                    }, i, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 13\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-32 bg-gray-200 rounded mt-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-10 bg-gray-200 rounded mt-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white p-6 rounded-lg border\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-6 bg-gray-200 rounded w-1/3 mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-64 bg-gray-200 rounded\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                lineNumber: 105,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n        lineNumber: 103,\n        columnNumber: 3\n    }, undefined);\n_c4 = PlaygroundSkeleton;\nconst GenericSkeleton = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6 animate-pulse\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-8 bg-gray-200 rounded w-1/3\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                lineNumber: 126,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-4 bg-gray-200 rounded w-2/3\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                lineNumber: 127,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-6 rounded-lg border\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        1,\n                        2,\n                        3,\n                        4,\n                        5\n                    ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-4 bg-gray-200 rounded\"\n                        }, i, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n                lineNumber: 128,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n        lineNumber: 125,\n        columnNumber: 3\n    }, undefined);\n_c5 = GenericSkeleton;\nfunction OptimisticPageLoader(param) {\n    let { targetRoute, children } = param;\n    _s();\n    const [showSkeleton, setShowSkeleton] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isContentReady, setIsContentReady] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const timeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const { isPageCached } = (0,_contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_3__.useNavigation)();\n    // Determine which skeleton to show based on target route\n    const getSkeletonComponent = (route)=>{\n        if (route.startsWith('/dashboard')) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DashboardSkeleton, {}, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n            lineNumber: 152,\n            columnNumber: 48\n        }, this);\n        if (route.startsWith('/pricing')) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PricingSkeleton, {}, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n            lineNumber: 153,\n            columnNumber: 46\n        }, this);\n        if (route.startsWith('/features')) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeaturesSkeleton, {}, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n            lineNumber: 154,\n            columnNumber: 47\n        }, this);\n        if (route.startsWith('/auth/')) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthSkeleton, {}, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n            lineNumber: 155,\n            columnNumber: 44\n        }, this);\n        if (route.startsWith('/playground')) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlaygroundSkeleton, {}, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n            lineNumber: 156,\n            columnNumber: 49\n        }, this);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GenericSkeleton, {}, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n            lineNumber: 157,\n            columnNumber: 12\n        }, this);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OptimisticPageLoader.useEffect\": ()=>{\n            // If we've reached the target route, start transitioning to real content\n            if (pathname === targetRoute) {\n                const isCached = isPageCached(targetRoute);\n                const delay = isCached ? 50 : 200; // Faster for cached pages\n                timeoutRef.current = setTimeout({\n                    \"OptimisticPageLoader.useEffect\": ()=>{\n                        setIsContentReady(true);\n                        // Hide skeleton after a brief moment to show real content\n                        setTimeout({\n                            \"OptimisticPageLoader.useEffect\": ()=>setShowSkeleton(false)\n                        }[\"OptimisticPageLoader.useEffect\"], 100);\n                    }\n                }[\"OptimisticPageLoader.useEffect\"], delay);\n            }\n            return ({\n                \"OptimisticPageLoader.useEffect\": ()=>{\n                    if (timeoutRef.current) {\n                        clearTimeout(timeoutRef.current);\n                    }\n                }\n            })[\"OptimisticPageLoader.useEffect\"];\n        }\n    }[\"OptimisticPageLoader.useEffect\"], [\n        pathname,\n        targetRoute,\n        isPageCached\n    ]);\n    // Reset state when target route changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OptimisticPageLoader.useEffect\": ()=>{\n            setShowSkeleton(true);\n            setIsContentReady(false);\n        }\n    }[\"OptimisticPageLoader.useEffect\"], [\n        targetRoute\n    ]);\n    // If we're not at the target route yet, show skeleton\n    if (pathname !== targetRoute && showSkeleton) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"optimistic-loading-container\",\n            children: getSkeletonComponent(targetRoute)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n            lineNumber: 189,\n            columnNumber: 7\n        }, this);\n    }\n    // If we're at the target route but content isn't ready, show skeleton with fade\n    if (pathname === targetRoute && showSkeleton && !isContentReady) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"optimistic-loading-container\",\n            children: getSkeletonComponent(targetRoute)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n            lineNumber: 198,\n            columnNumber: 7\n        }, this);\n    }\n    // Show real content with fade-in\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"transition-opacity duration-300 \".concat(isContentReady ? 'opacity-100' : 'opacity-0'),\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticPageLoader.tsx\",\n        lineNumber: 206,\n        columnNumber: 5\n    }, this);\n}\n_s(OptimisticPageLoader, \"iWvJQr8FDVjCJRkmW3jCl2DM26E=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_3__.useNavigation\n    ];\n});\n_c6 = OptimisticPageLoader;\n// Hook for components to trigger optimistic loading\nfunction useOptimisticNavigation() {\n    _s1();\n    const { navigateOptimistically } = (0,_contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_3__.useNavigation)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const navigateWithOptimisticLoading = (href)=>{\n        // Start optimistic loading immediately\n        navigateOptimistically(href);\n        // Also trigger Next.js router for actual navigation\n        router.prefetch(href);\n    };\n    return {\n        navigateWithOptimisticLoading\n    };\n}\n_s1(useOptimisticNavigation, \"azJjFO/g77+/6glRDPBj2X66N9c=\", false, function() {\n    return [\n        _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_3__.useNavigation,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"DashboardSkeleton\");\n$RefreshReg$(_c1, \"PricingSkeleton\");\n$RefreshReg$(_c2, \"FeaturesSkeleton\");\n$RefreshReg$(_c3, \"AuthSkeleton\");\n$RefreshReg$(_c4, \"PlaygroundSkeleton\");\n$RefreshReg$(_c5, \"GenericSkeleton\");\n$RefreshReg$(_c6, \"OptimisticPageLoader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/OptimisticPageLoader.tsx\n"));

/***/ })

});