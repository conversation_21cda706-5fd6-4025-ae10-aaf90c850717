"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"213be31bd394\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcUm9LZXkgQXBwXFxyb2tleS1hcHBcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjIxM2JlMzFiZDM5NFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Sidebar.tsx":
/*!************************************!*\
  !*** ./src/components/Sidebar.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/HomeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/KeyIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BeakerIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MapIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/AcademicCapIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/HomeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/KeyIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/BeakerIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/MapIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/AcademicCapIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,BeakerIcon,ChartBarIcon,DocumentTextIcon,HomeIcon,KeyIcon,MapIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/ChartBarIcon.js\");\n/* harmony import */ var _contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/SidebarContext */ \"(app-pages-browser)/./src/contexts/SidebarContext.tsx\");\n/* harmony import */ var _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/NavigationContext */ \"(app-pages-browser)/./src/contexts/NavigationContext.tsx\");\n/* harmony import */ var _hooks_useRoutePrefetch__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/useRoutePrefetch */ \"(app-pages-browser)/./src/hooks/useRoutePrefetch.ts\");\n/* harmony import */ var _hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useChatHistory */ \"(app-pages-browser)/./src/hooks/useChatHistory.ts\");\n/* harmony import */ var _hooks_usePredictiveNavigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/usePredictiveNavigation */ \"(app-pages-browser)/./src/hooks/usePredictiveNavigation.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst navItems = [\n    {\n        href: \"/dashboard\",\n        label: \"Dashboard\",\n        icon: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        iconSolid: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        description: \"Overview & analytics\"\n    },\n    {\n        href: \"/my-models\",\n        label: \"My Models\",\n        icon: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        iconSolid: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        description: \"API key management\"\n    },\n    {\n        href: \"/playground\",\n        label: \"Playground\",\n        icon: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n        iconSolid: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n        description: \"Test your models\"\n    },\n    {\n        href: \"/routing-setup\",\n        label: \"Routing Setup\",\n        icon: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n        iconSolid: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n        description: \"Configure routing\"\n    },\n    {\n        href: \"/logs\",\n        label: \"Logs\",\n        icon: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n        iconSolid: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n        description: \"Request history\"\n    },\n    {\n        href: \"/training\",\n        label: \"Prompt Engineering\",\n        icon: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n        iconSolid: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n        description: \"Custom prompts\"\n    },\n    {\n        href: \"/analytics\",\n        label: \"Analytics\",\n        icon: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n        iconSolid: _barrel_optimize_names_AcademicCapIcon_BeakerIcon_ChartBarIcon_DocumentTextIcon_HomeIcon_KeyIcon_MapIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n        description: \"Advanced insights\"\n    }\n];\nfunction Sidebar() {\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const { isCollapsed, isHovered, isHoverDisabled, setHovered } = (0,_contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_5__.useSidebar)();\n    const { navigateOptimistically } = (0,_contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_6__.useNavigation)();\n    const { prefetchOnHover } = (0,_hooks_useRoutePrefetch__WEBPACK_IMPORTED_MODULE_7__.useRoutePrefetch)();\n    const { prefetchWhenIdle } = (0,_hooks_useRoutePrefetch__WEBPACK_IMPORTED_MODULE_7__.useIntelligentPrefetch)();\n    const { prefetchChatHistory } = (0,_hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_8__.useChatHistoryPrefetch)();\n    const { predictions, isLearning } = (0,_hooks_usePredictiveNavigation__WEBPACK_IMPORTED_MODULE_9__.usePredictiveNavigation)();\n    const contextualSuggestions = (0,_hooks_usePredictiveNavigation__WEBPACK_IMPORTED_MODULE_9__.useContextualSuggestions)();\n    // Enhanced prefetching with predictive navigation\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)({\n        \"Sidebar.useEffect\": ()=>{\n            const allRoutes = navItems.map({\n                \"Sidebar.useEffect.allRoutes\": (item)=>item.href\n            }[\"Sidebar.useEffect.allRoutes\"]);\n            // Combine predictive routes with standard prefetching\n            const predictiveRoutes = predictions.slice(0, 2); // Top 2 predictions\n            const contextualRoutes = contextualSuggestions.filter({\n                \"Sidebar.useEffect.contextualRoutes\": (s)=>s.priority === 'high'\n            }[\"Sidebar.useEffect.contextualRoutes\"]).map({\n                \"Sidebar.useEffect.contextualRoutes\": (s)=>s.route\n            }[\"Sidebar.useEffect.contextualRoutes\"]).slice(0, 2);\n            const routesToPrefetch = [\n                ...predictiveRoutes,\n                ...contextualRoutes,\n                ...allRoutes.filter({\n                    \"Sidebar.useEffect.routesToPrefetch\": (route)=>route !== pathname && !predictiveRoutes.includes(route) && !contextualRoutes.includes(route)\n                }[\"Sidebar.useEffect.routesToPrefetch\"]),\n                '/playground',\n                '/logs'\n            ].slice(0, 6); // Increased limit for better coverage\n            console.log(\"\\uD83E\\uDDE0 [PREDICTIVE] Prefetching routes:\", {\n                predictive: predictiveRoutes,\n                contextual: contextualRoutes,\n                total: routesToPrefetch,\n                isLearning\n            });\n            const cleanup = prefetchWhenIdle(routesToPrefetch);\n            return cleanup;\n        }\n    }[\"Sidebar.useEffect\"], [\n        pathname,\n        prefetchWhenIdle,\n        predictions,\n        contextualSuggestions,\n        isLearning\n    ]);\n    // Determine if sidebar should be expanded (hover or not collapsed)\n    const isExpanded = !isCollapsed || isHovered;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: \"sidebar flex flex-col h-screen flex-shrink-0 transition-all duration-200 ease-out z-50 \".concat(isExpanded ? 'w-64' : 'w-16'),\n        onMouseEnter: ()=>!isHoverDisabled && setHovered(true),\n        onMouseLeave: ()=>!isHoverDisabled && setHovered(false),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 relative overflow-hidden\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 transition-all duration-200 ease-out \".concat(isExpanded ? 'px-6' : 'px-3'),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8 pt-4 transition-all duration-200 ease-out \".concat(isExpanded ? '' : 'text-center'),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative overflow-hidden\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"transition-all duration-200 ease-out \".concat(isExpanded ? 'opacity-0 scale-75 -translate-y-2' : 'opacity-100 scale-100 translate-y-0', \" \").concat(isExpanded ? 'absolute' : 'relative', \" w-8 h-8 bg-white rounded-lg flex items-center justify-center mx-auto p-1\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        src: \"/roukey_logo.png\",\n                                        alt: \"RouKey\",\n                                        width: 24,\n                                        height: 24,\n                                        className: \"object-contain\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"transition-all duration-200 ease-out \".concat(isExpanded ? 'opacity-100 scale-100 translate-y-0' : 'opacity-0 scale-75 translate-y-2', \" \").concat(isExpanded ? 'relative' : 'absolute top-0 left-0 w-full'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-2xl font-bold text-white tracking-tight whitespace-nowrap\",\n                                            children: \"RoKey\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-400 mt-1 whitespace-nowrap\",\n                                            children: \"Smart LLM Router\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"space-y-2\",\n                        children: navItems.map((item)=>{\n                            const isActive = pathname === item.href || pathname.startsWith(item.href + '/');\n                            const Icon = isActive ? item.iconSolid : item.icon;\n                            const isPredicted = predictions.includes(item.href);\n                            const contextualSuggestion = contextualSuggestions.find((s)=>s.route === item.href);\n                            // Enhanced prefetch for playground to include chat history\n                            const handlePlaygroundHover = ()=>{\n                                if (item.href === '/playground') {\n                                    // Prefetch route\n                                    prefetchOnHover(item.href, 50).onMouseEnter();\n                                    // Also prefetch chat history for current config if available\n                                    const currentConfigId = new URLSearchParams(window.location.search).get('config');\n                                    if (currentConfigId) {\n                                        prefetchChatHistory(currentConfigId);\n                                    }\n                                }\n                            };\n                            const hoverProps = item.href === '/playground' ? {\n                                onMouseEnter: handlePlaygroundHover\n                            } : prefetchOnHover(item.href, 50);\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: item.href,\n                                onClick: (e)=>{\n                                    e.preventDefault();\n                                    navigateOptimistically(item.href);\n                                },\n                                className: \"sidebar-nav-item group flex items-center transition-all duration-200 ease-out w-full text-left \".concat(isActive ? 'active' : '', \" \").concat(isExpanded ? '' : 'collapsed'),\n                                title: isExpanded ? undefined : item.label,\n                                ...hoverProps,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative flex items-center w-full overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative flex items-center justify-center transition-all duration-200 ease-out \".concat(isExpanded ? 'w-5 h-5 mr-3' : 'w-10 h-10 rounded-xl', \" \").concat(!isExpanded && isActive ? 'bg-white shadow-sm' : !isExpanded ? 'bg-transparent hover:bg-white/10' : ''),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                    className: \"transition-all duration-200 ease-out \".concat(isExpanded ? 'h-5 w-5' : 'h-5 w-5', \" \").concat(isActive ? 'text-orange-500' : 'text-white')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 23\n                                                }, this),\n                                                isPredicted && !isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute rounded-full bg-blue-400 animate-pulse transition-all duration-200 ease-out \".concat(isExpanded ? '-top-1 -right-1 w-2 h-2' : '-top-1 -right-1 w-3 h-3'),\n                                                    title: \"Predicted next destination\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 222,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 transition-all duration-200 ease-out \".concat(isExpanded ? 'opacity-100 translate-x-0 max-w-full' : 'opacity-0 translate-x-4 max-w-0'),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between whitespace-nowrap\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-medium text-sm\",\n                                                            children: item.label\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                            lineNumber: 235,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        contextualSuggestion && !isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs px-1.5 py-0.5 rounded-full ml-2 \".concat(contextualSuggestion.priority === 'high' ? 'bg-blue-500/20 text-blue-300' : 'bg-gray-500/20 text-gray-300'),\n                                                            children: contextualSuggestion.priority === 'high' ? '!' : '·'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                            lineNumber: 237,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs transition-colors duration-200 whitespace-nowrap \".concat(isActive ? 'text-orange-400' : 'text-gray-400'),\n                                                    children: contextualSuggestion ? contextualSuggestion.reason : item.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 19\n                                }, this)\n                            }, item.href, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 140,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n            lineNumber: 138,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\Sidebar.tsx\",\n        lineNumber: 131,\n        columnNumber: 5\n    }, this);\n}\n_s(Sidebar, \"eS+ydXAo96PN1Qszj9rKXwHTkQk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname,\n        _contexts_SidebarContext__WEBPACK_IMPORTED_MODULE_5__.useSidebar,\n        _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_6__.useNavigation,\n        _hooks_useRoutePrefetch__WEBPACK_IMPORTED_MODULE_7__.useRoutePrefetch,\n        _hooks_useRoutePrefetch__WEBPACK_IMPORTED_MODULE_7__.useIntelligentPrefetch,\n        _hooks_useChatHistory__WEBPACK_IMPORTED_MODULE_8__.useChatHistoryPrefetch,\n        _hooks_usePredictiveNavigation__WEBPACK_IMPORTED_MODULE_9__.usePredictiveNavigation,\n        _hooks_usePredictiveNavigation__WEBPACK_IMPORTED_MODULE_9__.useContextualSuggestions\n    ];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Sidebar.tsx\n"));

/***/ })

});