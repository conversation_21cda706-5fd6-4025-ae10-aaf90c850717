"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_app_features_page_tsx"],{

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowPathIcon.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/ArrowPathIcon.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction ArrowPathIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99\"\n    }));\n}\n_c = ArrowPathIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(ArrowPathIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"ArrowPathIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowPathIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction ChartBarIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z\"\n    }));\n}\n_c = ChartBarIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(ChartBarIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"ChartBarIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CloudIcon.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/CloudIcon.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction CloudIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M2.25 15a4.5 4.5 0 0 0 4.5 4.5H18a3.75 3.75 0 0 0 1.332-7.257 3 3 0 0 0-3.758-3.848 5.25 5.25 0 0 0-10.233 2.33A4.502 4.502 0 0 0 2.25 15Z\"\n    }));\n}\n_c = CloudIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(CloudIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"CloudIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CloudIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CodeBracketIcon.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/CodeBracketIcon.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction CodeBracketIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M17.25 6.75 22.5 12l-5.25 5.25m-10.5 0L1.5 12l5.25-5.25m7.5-3-4.5 16.5\"\n    }));\n}\n_c = CodeBracketIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(CodeBracketIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"CodeBracketIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CodeBracketIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CogIcon.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/CogIcon.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction CogIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M4.5 12a7.5 7.5 0 0 0 15 0m-15 0a7.5 7.5 0 1 1 15 0m-15 0H3m16.5 0H21m-1.5 0H12m-8.457 3.077 1.41-.513m14.095-5.13 1.41-.513M5.106 17.785l1.15-.964m11.49-9.642 1.149-.964M7.501 19.795l.75-1.3m7.5-12.99.75-1.3m-6.063 16.658.26-1.477m2.605-14.772.26-1.477m0 17.726-.26-1.477M10.698 4.614l-.26-1.477M16.5 19.794l-.75-1.299M7.5 4.205 12 12m6.894 5.785-1.149-.964M6.256 7.178l-1.15-.964m15.352 8.864-1.41-.513M4.954 9.435l-1.41-.514M12.002 12l-3.75 6.495\"\n    }));\n}\n_c = CogIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(CogIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"CogIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CogIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction CpuChipIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M8.25 3v1.5M4.5 8.25H3m18 0h-1.5M4.5 12H3m18 0h-1.5m-15 3.75H3m18 0h-1.5M8.25 19.5V21M12 3v1.5m0 15V21m3.75-18v1.5m0 15V21m-9-1.5h10.5a2.25 2.25 0 0 0 2.25-2.25V6.75a2.25 2.25 0 0 0-2.25-2.25H6.75A2.25 2.25 0 0 0 4.5 6.75v10.5a2.25 2.25 0 0 0 2.25 2.25Zm.75-12h9v9h-9v-9Z\"\n    }));\n}\n_c = CpuChipIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(CpuChipIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"CpuChipIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/LightBulbIcon.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/LightBulbIcon.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction LightBulbIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M12 18v-5.25m0 0a6.01 6.01 0 0 0 1.5-.189m-1.5.189a6.01 6.01 0 0 1-1.5-.189m3.75 7.478a12.06 12.06 0 0 1-4.5 0m3.75 2.383a14.406 14.406 0 0 1-3 0M14.25 18v-.192c0-.983.658-1.823 1.508-2.316a7.5 7.5 0 1 0-7.517 0c.85.493 1.509 1.333 1.509 2.316V18\"\n    }));\n}\n_c = LightBulbIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(LightBulbIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"LightBulbIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUvZXNtL0xpZ2h0QnVsYkljb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBK0I7QUFDL0IsU0FBU0MsY0FBYyxLQUl0QixFQUFFQyxNQUFNO1FBSmMsRUFDckJDLEtBQUssRUFDTEMsT0FBTyxFQUNQLEdBQUdDLE9BQ0osR0FKc0I7SUFLckIsT0FBTyxXQUFXLEdBQUVMLGdEQUFtQixDQUFDLE9BQU9PLE9BQU9DLE1BQU0sQ0FBQztRQUMzREMsT0FBTztRQUNQQyxNQUFNO1FBQ05DLFNBQVM7UUFDVEMsYUFBYTtRQUNiQyxRQUFRO1FBQ1IsZUFBZTtRQUNmLGFBQWE7UUFDYkMsS0FBS1o7UUFDTCxtQkFBbUJFO0lBQ3JCLEdBQUdDLFFBQVFGLFFBQVEsV0FBVyxHQUFFSCxnREFBbUIsQ0FBQyxTQUFTO1FBQzNEZSxJQUFJWDtJQUNOLEdBQUdELFNBQVMsTUFBTSxXQUFXLEdBQUVILGdEQUFtQixDQUFDLFFBQVE7UUFDekRnQixlQUFlO1FBQ2ZDLGdCQUFnQjtRQUNoQkMsR0FBRztJQUNMO0FBQ0Y7S0F0QlNqQjtBQXVCVCxNQUFNa0IsYUFBYSxXQUFXLEdBQUduQiw2Q0FBZ0IsQ0FBQ0M7O0FBQ2xELGlFQUFla0IsVUFBVUEsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFJvS2V5IEFwcFxccm9rZXktYXBwXFxub2RlX21vZHVsZXNcXEBoZXJvaWNvbnNcXHJlYWN0XFwyNFxcb3V0bGluZVxcZXNtXFxMaWdodEJ1bGJJY29uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuZnVuY3Rpb24gTGlnaHRCdWxiSWNvbih7XG4gIHRpdGxlLFxuICB0aXRsZUlkLFxuICAuLi5wcm9wc1xufSwgc3ZnUmVmKSB7XG4gIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcInN2Z1wiLCBPYmplY3QuYXNzaWduKHtcbiAgICB4bWxuczogXCJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Z1wiLFxuICAgIGZpbGw6IFwibm9uZVwiLFxuICAgIHZpZXdCb3g6IFwiMCAwIDI0IDI0XCIsXG4gICAgc3Ryb2tlV2lkdGg6IDEuNSxcbiAgICBzdHJva2U6IFwiY3VycmVudENvbG9yXCIsXG4gICAgXCJhcmlhLWhpZGRlblwiOiBcInRydWVcIixcbiAgICBcImRhdGEtc2xvdFwiOiBcImljb25cIixcbiAgICByZWY6IHN2Z1JlZixcbiAgICBcImFyaWEtbGFiZWxsZWRieVwiOiB0aXRsZUlkXG4gIH0sIHByb3BzKSwgdGl0bGUgPyAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcInRpdGxlXCIsIHtcbiAgICBpZDogdGl0bGVJZFxuICB9LCB0aXRsZSkgOiBudWxsLCAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcInBhdGhcIiwge1xuICAgIHN0cm9rZUxpbmVjYXA6IFwicm91bmRcIixcbiAgICBzdHJva2VMaW5lam9pbjogXCJyb3VuZFwiLFxuICAgIGQ6IFwiTTEyIDE4di01LjI1bTAgMGE2LjAxIDYuMDEgMCAwIDAgMS41LS4xODltLTEuNS4xODlhNi4wMSA2LjAxIDAgMCAxLTEuNS0uMTg5bTMuNzUgNy40NzhhMTIuMDYgMTIuMDYgMCAwIDEtNC41IDBtMy43NSAyLjM4M2ExNC40MDYgMTQuNDA2IDAgMCAxLTMgME0xNC4yNSAxOHYtLjE5MmMwLS45ODMuNjU4LTEuODIzIDEuNTA4LTIuMzE2YTcuNSA3LjUgMCAxIDAtNy41MTcgMGMuODUuNDkzIDEuNTA5IDEuMzMzIDEuNTA5IDIuMzE2VjE4XCJcbiAgfSkpO1xufVxuY29uc3QgRm9yd2FyZFJlZiA9IC8qI19fUFVSRV9fKi8gUmVhY3QuZm9yd2FyZFJlZihMaWdodEJ1bGJJY29uKTtcbmV4cG9ydCBkZWZhdWx0IEZvcndhcmRSZWY7Il0sIm5hbWVzIjpbIlJlYWN0IiwiTGlnaHRCdWxiSWNvbiIsInN2Z1JlZiIsInRpdGxlIiwidGl0bGVJZCIsInByb3BzIiwiY3JlYXRlRWxlbWVudCIsIk9iamVjdCIsImFzc2lnbiIsInhtbG5zIiwiZmlsbCIsInZpZXdCb3giLCJzdHJva2VXaWR0aCIsInN0cm9rZSIsInJlZiIsImlkIiwic3Ryb2tlTGluZWNhcCIsInN0cm9rZUxpbmVqb2luIiwiZCIsIkZvcndhcmRSZWYiLCJmb3J3YXJkUmVmIl0sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/LightBulbIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/RocketLaunchIcon.js":
/*!**************************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/RocketLaunchIcon.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction RocketLaunchIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M15.59 14.37a6 6 0 0 1-5.84 7.38v-4.8m5.84-2.58a14.98 14.98 0 0 0 6.16-12.12A14.98 14.98 0 0 0 9.631 8.41m5.96 5.96a14.926 14.926 0 0 1-5.841 2.58m-.119-8.54a6 6 0 0 0-7.381 5.84h4.8m2.581-5.84a14.927 14.927 0 0 0-2.58 5.84m2.699 2.7c-.103.021-.207.041-.311.06a15.09 15.09 0 0 1-2.448-2.448 14.9 14.9 0 0 1 .06-.312m-2.24 2.39a4.493 4.493 0 0 0-1.757 4.306 4.493 4.493 0 0 0 4.306-1.758M16.5 9a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Z\"\n    }));\n}\n_c = RocketLaunchIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(RocketLaunchIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"RocketLaunchIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUvZXNtL1JvY2tldExhdW5jaEljb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBK0I7QUFDL0IsU0FBU0MsaUJBQWlCLEtBSXpCLEVBQUVDLE1BQU07UUFKaUIsRUFDeEJDLEtBQUssRUFDTEMsT0FBTyxFQUNQLEdBQUdDLE9BQ0osR0FKeUI7SUFLeEIsT0FBTyxXQUFXLEdBQUVMLGdEQUFtQixDQUFDLE9BQU9PLE9BQU9DLE1BQU0sQ0FBQztRQUMzREMsT0FBTztRQUNQQyxNQUFNO1FBQ05DLFNBQVM7UUFDVEMsYUFBYTtRQUNiQyxRQUFRO1FBQ1IsZUFBZTtRQUNmLGFBQWE7UUFDYkMsS0FBS1o7UUFDTCxtQkFBbUJFO0lBQ3JCLEdBQUdDLFFBQVFGLFFBQVEsV0FBVyxHQUFFSCxnREFBbUIsQ0FBQyxTQUFTO1FBQzNEZSxJQUFJWDtJQUNOLEdBQUdELFNBQVMsTUFBTSxXQUFXLEdBQUVILGdEQUFtQixDQUFDLFFBQVE7UUFDekRnQixlQUFlO1FBQ2ZDLGdCQUFnQjtRQUNoQkMsR0FBRztJQUNMO0FBQ0Y7S0F0QlNqQjtBQXVCVCxNQUFNa0IsYUFBYSxXQUFXLEdBQUduQiw2Q0FBZ0IsQ0FBQ0M7O0FBQ2xELGlFQUFla0IsVUFBVUEsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFJvS2V5IEFwcFxccm9rZXktYXBwXFxub2RlX21vZHVsZXNcXEBoZXJvaWNvbnNcXHJlYWN0XFwyNFxcb3V0bGluZVxcZXNtXFxSb2NrZXRMYXVuY2hJY29uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuZnVuY3Rpb24gUm9ja2V0TGF1bmNoSWNvbih7XG4gIHRpdGxlLFxuICB0aXRsZUlkLFxuICAuLi5wcm9wc1xufSwgc3ZnUmVmKSB7XG4gIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcInN2Z1wiLCBPYmplY3QuYXNzaWduKHtcbiAgICB4bWxuczogXCJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Z1wiLFxuICAgIGZpbGw6IFwibm9uZVwiLFxuICAgIHZpZXdCb3g6IFwiMCAwIDI0IDI0XCIsXG4gICAgc3Ryb2tlV2lkdGg6IDEuNSxcbiAgICBzdHJva2U6IFwiY3VycmVudENvbG9yXCIsXG4gICAgXCJhcmlhLWhpZGRlblwiOiBcInRydWVcIixcbiAgICBcImRhdGEtc2xvdFwiOiBcImljb25cIixcbiAgICByZWY6IHN2Z1JlZixcbiAgICBcImFyaWEtbGFiZWxsZWRieVwiOiB0aXRsZUlkXG4gIH0sIHByb3BzKSwgdGl0bGUgPyAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcInRpdGxlXCIsIHtcbiAgICBpZDogdGl0bGVJZFxuICB9LCB0aXRsZSkgOiBudWxsLCAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcInBhdGhcIiwge1xuICAgIHN0cm9rZUxpbmVjYXA6IFwicm91bmRcIixcbiAgICBzdHJva2VMaW5lam9pbjogXCJyb3VuZFwiLFxuICAgIGQ6IFwiTTE1LjU5IDE0LjM3YTYgNiAwIDAgMS01Ljg0IDcuMzh2LTQuOG01Ljg0LTIuNThhMTQuOTggMTQuOTggMCAwIDAgNi4xNi0xMi4xMkExNC45OCAxNC45OCAwIDAgMCA5LjYzMSA4LjQxbTUuOTYgNS45NmExNC45MjYgMTQuOTI2IDAgMCAxLTUuODQxIDIuNThtLS4xMTktOC41NGE2IDYgMCAwIDAtNy4zODEgNS44NGg0LjhtMi41ODEtNS44NGExNC45MjcgMTQuOTI3IDAgMCAwLTIuNTggNS44NG0yLjY5OSAyLjdjLS4xMDMuMDIxLS4yMDcuMDQxLS4zMTEuMDZhMTUuMDkgMTUuMDkgMCAwIDEtMi40NDgtMi40NDggMTQuOSAxNC45IDAgMCAxIC4wNi0uMzEybS0yLjI0IDIuMzlhNC40OTMgNC40OTMgMCAwIDAtMS43NTcgNC4zMDYgNC40OTMgNC40OTMgMCAwIDAgNC4zMDYtMS43NThNMTYuNSA5YTEuNSAxLjUgMCAxIDEtMyAwIDEuNSAxLjUgMCAwIDEgMyAwWlwiXG4gIH0pKTtcbn1cbmNvbnN0IEZvcndhcmRSZWYgPSAvKiNfX1BVUkVfXyovIFJlYWN0LmZvcndhcmRSZWYoUm9ja2V0TGF1bmNoSWNvbik7XG5leHBvcnQgZGVmYXVsdCBGb3J3YXJkUmVmOyJdLCJuYW1lcyI6WyJSZWFjdCIsIlJvY2tldExhdW5jaEljb24iLCJzdmdSZWYiLCJ0aXRsZSIsInRpdGxlSWQiLCJwcm9wcyIsImNyZWF0ZUVsZW1lbnQiLCJPYmplY3QiLCJhc3NpZ24iLCJ4bWxucyIsImZpbGwiLCJ2aWV3Qm94Iiwic3Ryb2tlV2lkdGgiLCJzdHJva2UiLCJyZWYiLCJpZCIsInN0cm9rZUxpbmVjYXAiLCJzdHJva2VMaW5lam9pbiIsImQiLCJGb3J3YXJkUmVmIiwiZm9yd2FyZFJlZiJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/RocketLaunchIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/features/page.tsx":
/*!***********************************!*\
  !*** ./src/app/features/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FeaturesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,CloudIcon,CodeBracketIcon,CogIcon,CpuChipIcon,LightBulbIcon,RocketLaunchIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,CloudIcon,CodeBracketIcon,CogIcon,CpuChipIcon,LightBulbIcon,RocketLaunchIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,CloudIcon,CodeBracketIcon,CogIcon,CpuChipIcon,LightBulbIcon,RocketLaunchIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,CloudIcon,CodeBracketIcon,CogIcon,CpuChipIcon,LightBulbIcon,RocketLaunchIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,CloudIcon,CodeBracketIcon,CogIcon,CpuChipIcon,LightBulbIcon,RocketLaunchIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowPathIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,CloudIcon,CodeBracketIcon,CogIcon,CpuChipIcon,LightBulbIcon,RocketLaunchIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CloudIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,CloudIcon,CodeBracketIcon,CogIcon,CpuChipIcon,LightBulbIcon,RocketLaunchIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CodeBracketIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,CloudIcon,CodeBracketIcon,CogIcon,CpuChipIcon,LightBulbIcon,RocketLaunchIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/LightBulbIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,CloudIcon,CodeBracketIcon,CogIcon,CpuChipIcon,LightBulbIcon,RocketLaunchIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/RocketLaunchIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,CloudIcon,CodeBracketIcon,CogIcon,CpuChipIcon,LightBulbIcon,RocketLaunchIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CogIcon.js\");\n/* harmony import */ var _components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/landing/LandingNavbar */ \"(app-pages-browser)/./src/components/landing/LandingNavbar.tsx\");\n/* harmony import */ var _hooks_usePerformanceOptimization__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/usePerformanceOptimization */ \"(app-pages-browser)/./src/hooks/usePerformanceOptimization.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n// Lazy load footer for better initial load\nconst Footer = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.lazy)(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_landing_Footer_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/landing/Footer */ \"(app-pages-browser)/./src/components/landing/Footer.tsx\")));\n_c = Footer;\nconst features = [\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: \"Intelligent Routing\",\n        description: \"Advanced AI algorithms analyze your prompts and automatically route them to the optimal model for best results.\",\n        details: [\n            \"Real-time prompt analysis\",\n            \"Context-aware routing decisions\",\n            \"Performance optimization\",\n            \"Cost-effective model selection\"\n        ]\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        title: \"Enterprise Security\",\n        description: \"Bank-grade security with end-to-end encryption, compliance certifications, and audit trails.\",\n        details: [\n            \"SOC 2 Type II compliance\",\n            \"End-to-end encryption\",\n            \"Complete audit trails\",\n            \"Role-based access control\"\n        ]\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        title: \"Advanced Analytics\",\n        description: \"Comprehensive insights into model performance, costs, and usage patterns with real-time dashboards.\",\n        details: [\n            \"Real-time performance metrics\",\n            \"Cost optimization insights\",\n            \"Usage pattern analysis\",\n            \"Custom reporting\"\n        ]\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        title: \"300+ AI Models\",\n        description: \"Access to the largest collection of AI models from leading providers, all through one unified API.\",\n        details: [\n            \"OpenAI, Anthropic, Google, Meta\",\n            \"Specialized domain models\",\n            \"Latest model versions\",\n            \"Custom model integration\"\n        ]\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        title: \"Auto-Failover\",\n        description: \"Automatic failover and retry mechanisms ensure 99.9% uptime for your AI applications.\",\n        details: [\n            \"Intelligent failover routing\",\n            \"Automatic retry logic\",\n            \"Health monitoring\",\n            \"Zero-downtime switching\"\n        ]\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        title: \"Global Infrastructure\",\n        description: \"Distributed infrastructure across multiple regions for low latency and high availability.\",\n        details: [\n            \"Multi-region deployment\",\n            \"Edge computing optimization\",\n            \"CDN acceleration\",\n            \"99.9% uptime SLA\"\n        ]\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        title: \"Developer-First\",\n        description: \"Built by developers, for developers. Simple APIs, comprehensive docs, and powerful SDKs.\",\n        details: [\n            \"RESTful API design\",\n            \"Multiple SDK languages\",\n            \"Interactive documentation\",\n            \"Code examples & tutorials\"\n        ]\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        title: \"Smart Optimization\",\n        description: \"Continuous learning algorithms optimize routing decisions based on your usage patterns.\",\n        details: [\n            \"Machine learning optimization\",\n            \"Pattern recognition\",\n            \"Adaptive routing\",\n            \"Performance tuning\"\n        ]\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        title: \"Rapid Deployment\",\n        description: \"Get started in minutes with our simple integration process and migration tools.\",\n        details: [\n            \"5-minute setup\",\n            \"Migration assistance\",\n            \"Zero-code integration\",\n            \"Instant activation\"\n        ]\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        title: \"Custom Configurations\",\n        description: \"Flexible routing rules and custom configurations to match your specific requirements.\",\n        details: [\n            \"Custom routing logic\",\n            \"Business rule engine\",\n            \"A/B testing support\",\n            \"Configuration templates\"\n        ]\n    }\n];\nfunction FeaturesPage() {\n    _s();\n    const { startMeasurement, endMeasurement } = (0,_hooks_usePerformanceOptimization__WEBPACK_IMPORTED_MODULE_3__.usePerformanceOptimization)('FeaturesPage', {\n        enableMonitoring: true,\n        enableMemoryTracking: true,\n        warningThresholds: {\n            renderTime: 200,\n            memoryUsage: 40 * 1024 * 1024 // 40MB\n        }\n    });\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"FeaturesPage.useEffect\": ()=>{\n            startMeasurement();\n            return ({\n                \"FeaturesPage.useEffect\": ()=>endMeasurement()\n            })[\"FeaturesPage.useEffect\"];\n        }\n    }[\"FeaturesPage.useEffect\"], [\n        startMeasurement,\n        endMeasurement\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                lineNumber: 153,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"pt-20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20 bg-gradient-to-br from-slate-50 to-blue-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-5xl md:text-6xl font-bold text-gray-900 mb-6\",\n                                        children: [\n                                            \"Powerful Features for\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-[#ff6b35] block\",\n                                                children: \"Modern AI Development\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-gray-600 max-w-3xl mx-auto mb-8\",\n                                        children: \"Everything you need to build, deploy, and scale AI applications with confidence. From intelligent routing to enterprise security, we've got you covered.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-12\",\n                                children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.6,\n                                            delay: index * 0.1\n                                        },\n                                        className: \"bg-white rounded-2xl p-8 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-12 h-12 bg-[#ff6b35] rounded-xl flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                            className: \"w-6 h-6 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                                            lineNumber: 191,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                                        lineNumber: 190,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                                    lineNumber: 189,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-semibold text-gray-900 mb-3\",\n                                                            children: feature.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                                            lineNumber: 195,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 mb-4\",\n                                                            children: feature.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                                            lineNumber: 198,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"space-y-2\",\n                                                            children: feature.details.map((detail, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    className: \"flex items-center text-sm text-gray-500\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-1.5 h-1.5 bg-[#ff6b35] rounded-full mr-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                                                            lineNumber: 204,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        detail\n                                                                    ]\n                                                                }, idx, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                                                    lineNumber: 203,\n                                                                    columnNumber: 27\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                                            lineNumber: 201,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, feature.title, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20 bg-gray-900\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-4xl font-bold text-white mb-6\",\n                                        children: \"Ready to Transform Your AI Development?\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-gray-300 mb-8\",\n                                        children: \"Join thousands of developers who trust RouKey for their AI infrastructure.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.button, {\n                                        whileHover: {\n                                            scale: 1.05\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        className: \"bg-[#ff6b35] text-white px-8 py-4 rounded-xl font-semibold text-lg hover:bg-[#e55a2b] transition-colors duration-200 shadow-lg\",\n                                        children: \"Get Started Now\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                lineNumber: 155,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n                fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-32 bg-gray-900\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                    lineNumber: 243,\n                    columnNumber: 27\n                }, void 0),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Footer, {}, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                    lineNumber: 244,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                lineNumber: 243,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n        lineNumber: 152,\n        columnNumber: 5\n    }, this);\n}\n_s(FeaturesPage, \"Q+OT+PfUl/epZO1+DioOG7h1YRE=\", false, function() {\n    return [\n        _hooks_usePerformanceOptimization__WEBPACK_IMPORTED_MODULE_3__.usePerformanceOptimization\n    ];\n});\n_c1 = FeaturesPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"Footer\");\n$RefreshReg$(_c1, \"FeaturesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/features/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/usePerformanceOptimization.ts":
/*!*************************************************!*\
  !*** ./src/hooks/usePerformanceOptimization.ts ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   usePageLoadPerformance: () => (/* binding */ usePageLoadPerformance),\n/* harmony export */   usePerformanceOptimization: () => (/* binding */ usePerformanceOptimization)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ usePerformanceOptimization,usePageLoadPerformance auto */ \nfunction usePerformanceOptimization(componentName) {\n    let config = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const { enableMonitoring = true, enableMemoryTracking = true, enableBundleAnalysis = false, enableCacheTracking = true, warningThresholds = {\n        renderTime: 100,\n        memoryUsage: 50 * 1024 * 1024,\n        bundleSize: 1024 * 1024 // 1MB\n    } } = config;\n    const [metrics, setMetrics] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        renderTime: 0\n    });\n    const renderStartTime = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    const cacheHits = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    const cacheRequests = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    // Start performance measurement\n    const startMeasurement = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"usePerformanceOptimization.useCallback[startMeasurement]\": ()=>{\n            if (!enableMonitoring) return;\n            renderStartTime.current = performance.now();\n        }\n    }[\"usePerformanceOptimization.useCallback[startMeasurement]\"], [\n        enableMonitoring\n    ]);\n    // End performance measurement\n    const endMeasurement = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"usePerformanceOptimization.useCallback[endMeasurement]\": ()=>{\n            if (!enableMonitoring || !renderStartTime.current) return;\n            const renderTime = performance.now() - renderStartTime.current;\n            setMetrics({\n                \"usePerformanceOptimization.useCallback[endMeasurement]\": (prev)=>({\n                        ...prev,\n                        renderTime\n                    })\n            }[\"usePerformanceOptimization.useCallback[endMeasurement]\"]);\n            // Log warnings if thresholds exceeded\n            if (renderTime > (warningThresholds.renderTime || 100)) {\n                console.warn(\"⚠️ \".concat(componentName, \" slow render: \").concat(renderTime.toFixed(2), \"ms\"));\n            } else if (renderTime < 16) {\n                console.log(\"✅ \".concat(componentName, \" fast render: \").concat(renderTime.toFixed(2), \"ms\"));\n            }\n            renderStartTime.current = 0;\n        }\n    }[\"usePerformanceOptimization.useCallback[endMeasurement]\"], [\n        componentName,\n        enableMonitoring,\n        warningThresholds.renderTime\n    ]);\n    // Memory usage tracking\n    const trackMemoryUsage = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"usePerformanceOptimization.useCallback[trackMemoryUsage]\": ()=>{\n            if (!enableMemoryTracking || !('memory' in performance)) return;\n            const memory = performance.memory;\n            const memoryUsage = {\n                used: memory.usedJSHeapSize,\n                total: memory.totalJSHeapSize,\n                limit: memory.jsHeapSizeLimit\n            };\n            setMetrics({\n                \"usePerformanceOptimization.useCallback[trackMemoryUsage]\": (prev)=>({\n                        ...prev,\n                        memoryUsage\n                    })\n            }[\"usePerformanceOptimization.useCallback[trackMemoryUsage]\"]);\n            // Warning for high memory usage\n            if (memoryUsage.used > (warningThresholds.memoryUsage || 50 * 1024 * 1024)) {\n                console.warn(\"⚠️ High memory usage in \".concat(componentName, \": \").concat((memoryUsage.used / 1024 / 1024).toFixed(2), \"MB\"));\n            }\n        }\n    }[\"usePerformanceOptimization.useCallback[trackMemoryUsage]\"], [\n        componentName,\n        enableMemoryTracking,\n        warningThresholds.memoryUsage\n    ]);\n    // Bundle size analysis\n    const analyzeBundleSize = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"usePerformanceOptimization.useCallback[analyzeBundleSize]\": ()=>{\n            if (!enableBundleAnalysis) return;\n            const resources = performance.getEntriesByType('resource');\n            let totalJSSize = 0;\n            resources.forEach({\n                \"usePerformanceOptimization.useCallback[analyzeBundleSize]\": (resource)=>{\n                    if (resource.name.includes('.js') && resource.transferSize) {\n                        totalJSSize += resource.transferSize;\n                    }\n                }\n            }[\"usePerformanceOptimization.useCallback[analyzeBundleSize]\"]);\n            setMetrics({\n                \"usePerformanceOptimization.useCallback[analyzeBundleSize]\": (prev)=>({\n                        ...prev,\n                        bundleSize: totalJSSize\n                    })\n            }[\"usePerformanceOptimization.useCallback[analyzeBundleSize]\"]);\n            if (totalJSSize > (warningThresholds.bundleSize || 1024 * 1024)) {\n                console.warn(\"⚠️ Large bundle size: \".concat((totalJSSize / 1024 / 1024).toFixed(2), \"MB\"));\n            }\n        }\n    }[\"usePerformanceOptimization.useCallback[analyzeBundleSize]\"], [\n        enableBundleAnalysis,\n        warningThresholds.bundleSize\n    ]);\n    // Cache hit rate tracking\n    const trackCacheHitRate = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"usePerformanceOptimization.useCallback[trackCacheHitRate]\": ()=>{\n            if (!enableCacheTracking) return;\n            const hitRate = cacheRequests.current > 0 ? cacheHits.current / cacheRequests.current * 100 : 0;\n            setMetrics({\n                \"usePerformanceOptimization.useCallback[trackCacheHitRate]\": (prev)=>({\n                        ...prev,\n                        cacheHitRate: hitRate\n                    })\n            }[\"usePerformanceOptimization.useCallback[trackCacheHitRate]\"]);\n        }\n    }[\"usePerformanceOptimization.useCallback[trackCacheHitRate]\"], [\n        enableCacheTracking\n    ]);\n    // Service Worker message handler for cache events\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"usePerformanceOptimization.useEffect\": ()=>{\n            if (!enableCacheTracking || \"object\" === 'undefined') return;\n            const handleSWMessage = {\n                \"usePerformanceOptimization.useEffect.handleSWMessage\": (event)=>{\n                    var _event_data, _event_data1;\n                    if (((_event_data = event.data) === null || _event_data === void 0 ? void 0 : _event_data.type) === 'CACHE_HIT') {\n                        cacheHits.current++;\n                        cacheRequests.current++;\n                        trackCacheHitRate();\n                    } else if (((_event_data1 = event.data) === null || _event_data1 === void 0 ? void 0 : _event_data1.type) === 'CACHE_MISS') {\n                        cacheRequests.current++;\n                        trackCacheHitRate();\n                    }\n                }\n            }[\"usePerformanceOptimization.useEffect.handleSWMessage\"];\n            if ('serviceWorker' in navigator) {\n                navigator.serviceWorker.addEventListener('message', handleSWMessage);\n                return ({\n                    \"usePerformanceOptimization.useEffect\": ()=>{\n                        navigator.serviceWorker.removeEventListener('message', handleSWMessage);\n                    }\n                })[\"usePerformanceOptimization.useEffect\"];\n            }\n        }\n    }[\"usePerformanceOptimization.useEffect\"], [\n        enableCacheTracking,\n        trackCacheHitRate\n    ]);\n    // Navigation timing\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"usePerformanceOptimization.useEffect\": ()=>{\n            if (!enableMonitoring) return;\n            const measureNavigation = {\n                \"usePerformanceOptimization.useEffect.measureNavigation\": ()=>{\n                    const navigation = performance.getEntriesByType('navigation')[0];\n                    if (navigation) {\n                        const navigationTime = navigation.loadEventEnd - navigation.navigationStart;\n                        setMetrics({\n                            \"usePerformanceOptimization.useEffect.measureNavigation\": (prev)=>({\n                                    ...prev,\n                                    navigationTime\n                                })\n                        }[\"usePerformanceOptimization.useEffect.measureNavigation\"]);\n                    }\n                }\n            }[\"usePerformanceOptimization.useEffect.measureNavigation\"];\n            // Measure after page load\n            if (document.readyState === 'complete') {\n                measureNavigation();\n            } else {\n                window.addEventListener('load', measureNavigation);\n                return ({\n                    \"usePerformanceOptimization.useEffect\": ()=>window.removeEventListener('load', measureNavigation)\n                })[\"usePerformanceOptimization.useEffect\"];\n            }\n        }\n    }[\"usePerformanceOptimization.useEffect\"], [\n        enableMonitoring\n    ]);\n    // Periodic monitoring\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"usePerformanceOptimization.useEffect\": ()=>{\n            if (!enableMonitoring) return;\n            const interval = setInterval({\n                \"usePerformanceOptimization.useEffect.interval\": ()=>{\n                    trackMemoryUsage();\n                    analyzeBundleSize();\n                }\n            }[\"usePerformanceOptimization.useEffect.interval\"], 5000); // Check every 5 seconds\n            return ({\n                \"usePerformanceOptimization.useEffect\": ()=>clearInterval(interval)\n            })[\"usePerformanceOptimization.useEffect\"];\n        }\n    }[\"usePerformanceOptimization.useEffect\"], [\n        enableMonitoring,\n        trackMemoryUsage,\n        analyzeBundleSize\n    ]);\n    // Performance optimization suggestions\n    const getOptimizationSuggestions = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"usePerformanceOptimization.useCallback[getOptimizationSuggestions]\": ()=>{\n            const suggestions = [];\n            if (metrics.renderTime > 100) {\n                suggestions.push('Consider memoizing expensive calculations');\n                suggestions.push('Use React.memo for component optimization');\n                suggestions.push('Implement virtualization for large lists');\n            }\n            if (metrics.memoryUsage && metrics.memoryUsage.used > 50 * 1024 * 1024) {\n                suggestions.push('Check for memory leaks');\n                suggestions.push('Optimize image sizes and formats');\n                suggestions.push('Implement proper cleanup in useEffect');\n            }\n            if (metrics.bundleSize && metrics.bundleSize > 1024 * 1024) {\n                suggestions.push('Implement code splitting');\n                suggestions.push('Use dynamic imports for heavy components');\n                suggestions.push('Remove unused dependencies');\n            }\n            if (metrics.cacheHitRate !== undefined && metrics.cacheHitRate < 70) {\n                suggestions.push('Improve caching strategy');\n                suggestions.push('Implement service worker caching');\n                suggestions.push('Use browser cache headers');\n            }\n            return suggestions;\n        }\n    }[\"usePerformanceOptimization.useCallback[getOptimizationSuggestions]\"], [\n        metrics\n    ]);\n    // Export performance data for debugging\n    const exportMetrics = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"usePerformanceOptimization.useCallback[exportMetrics]\": ()=>{\n            const exportData = {\n                component: componentName,\n                timestamp: new Date().toISOString(),\n                metrics,\n                suggestions: getOptimizationSuggestions(),\n                userAgent: navigator.userAgent,\n                url: window.location.href\n            };\n            console.group(\"\\uD83D\\uDCCA Performance Report: \".concat(componentName));\n            console.table(metrics);\n            console.log('Suggestions:', getOptimizationSuggestions());\n            console.groupEnd();\n            return exportData;\n        }\n    }[\"usePerformanceOptimization.useCallback[exportMetrics]\"], [\n        componentName,\n        metrics,\n        getOptimizationSuggestions\n    ]);\n    return {\n        metrics,\n        startMeasurement,\n        endMeasurement,\n        trackMemoryUsage,\n        analyzeBundleSize,\n        trackCacheHitRate,\n        getOptimizationSuggestions,\n        exportMetrics\n    };\n}\n// Hook for monitoring page load performance\nfunction usePageLoadPerformance() {\n    const [loadMetrics, setLoadMetrics] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({});\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"usePageLoadPerformance.useEffect\": ()=>{\n            const measurePageLoad = {\n                \"usePageLoadPerformance.useEffect.measurePageLoad\": ()=>{\n                    const navigation = performance.getEntriesByType('navigation')[0];\n                    const paint = performance.getEntriesByType('paint');\n                    const metrics = {};\n                    if (navigation) {\n                        metrics.domContentLoaded = navigation.domContentLoadedEventEnd - navigation.navigationStart;\n                    }\n                    paint.forEach({\n                        \"usePageLoadPerformance.useEffect.measurePageLoad\": (entry)=>{\n                            if (entry.name === 'first-paint') {\n                                metrics.firstPaint = entry.startTime;\n                            } else if (entry.name === 'first-contentful-paint') {\n                                metrics.firstContentfulPaint = entry.startTime;\n                            }\n                        }\n                    }[\"usePageLoadPerformance.useEffect.measurePageLoad\"]);\n                    // Largest Contentful Paint\n                    if ('PerformanceObserver' in window) {\n                        const observer = new PerformanceObserver({\n                            \"usePageLoadPerformance.useEffect.measurePageLoad\": (list)=>{\n                                const entries = list.getEntries();\n                                const lastEntry = entries[entries.length - 1];\n                                metrics.largestContentfulPaint = lastEntry.startTime;\n                                setLoadMetrics({\n                                    \"usePageLoadPerformance.useEffect.measurePageLoad\": (prev)=>({\n                                            ...prev,\n                                            ...metrics\n                                        })\n                                }[\"usePageLoadPerformance.useEffect.measurePageLoad\"]);\n                            }\n                        }[\"usePageLoadPerformance.useEffect.measurePageLoad\"]);\n                        observer.observe({\n                            entryTypes: [\n                                'largest-contentful-paint'\n                            ]\n                        });\n                    }\n                    setLoadMetrics({\n                        \"usePageLoadPerformance.useEffect.measurePageLoad\": (prev)=>({\n                                ...prev,\n                                ...metrics\n                            })\n                    }[\"usePageLoadPerformance.useEffect.measurePageLoad\"]);\n                }\n            }[\"usePageLoadPerformance.useEffect.measurePageLoad\"];\n            if (document.readyState === 'complete') {\n                measurePageLoad();\n            } else {\n                window.addEventListener('load', measurePageLoad);\n                return ({\n                    \"usePageLoadPerformance.useEffect\": ()=>window.removeEventListener('load', measurePageLoad)\n                })[\"usePageLoadPerformance.useEffect\"];\n            }\n        }\n    }[\"usePageLoadPerformance.useEffect\"], []);\n    return loadMetrics;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/usePerformanceOptimization.ts\n"));

/***/ })

}]);