"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/contexts/NavigationContext.tsx":
/*!********************************************!*\
  !*** ./src/contexts/NavigationContext.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NavigationProvider: () => (/* binding */ NavigationProvider),\n/* harmony export */   useNavigation: () => (/* binding */ useNavigation),\n/* harmony export */   useNavigationSafe: () => (/* binding */ useNavigationSafe)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ NavigationProvider,useNavigation,useNavigationSafe auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\nconst NavigationContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction NavigationProvider(param) {\n    let { children } = param;\n    _s();\n    const [isNavigating, setIsNavigating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [targetRoute, setTargetRoute] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [navigationHistory, setNavigationHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [cachedPages, setCachedPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const timeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const navigationQueueRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const currentNavigationIdRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const lastNavigationTimeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    const clickCountRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({});\n    const clickTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({});\n    // Handle client-side hydration\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NavigationProvider.useEffect\": ()=>{\n            setIsClient(true);\n        }\n    }[\"NavigationProvider.useEffect\"], []);\n    // Safe console logging that only runs on client\n    const safeLog = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NavigationProvider.useCallback[safeLog]\": (message)=>{\n            if (isClient && \"object\" !== 'undefined') {\n                console.log(message);\n            }\n        }\n    }[\"NavigationProvider.useCallback[safeLog]\"], [\n        isClient\n    ]);\n    // Track navigation history and cache pages\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NavigationProvider.useEffect\": ()=>{\n            if (pathname && !navigationHistory.includes(pathname)) {\n                setNavigationHistory({\n                    \"NavigationProvider.useEffect\": (prev)=>[\n                            ...prev,\n                            pathname\n                        ]\n                }[\"NavigationProvider.useEffect\"]);\n                setCachedPages({\n                    \"NavigationProvider.useEffect\": (prev)=>new Set([\n                            ...prev,\n                            pathname\n                        ])\n                }[\"NavigationProvider.useEffect\"]);\n            }\n        }\n    }[\"NavigationProvider.useEffect\"], [\n        pathname,\n        navigationHistory\n    ]);\n    // Clear navigation state when route actually changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NavigationProvider.useEffect\": ()=>{\n            safeLog(\"\\uD83D\\uDD0D [OPTIMISTIC NAV] Route check: target=\".concat(targetRoute, \", current=\").concat(pathname, \", navigationId=\").concat(currentNavigationIdRef.current));\n            if (targetRoute && currentNavigationIdRef.current) {\n                // Exact route matching only - be more precise\n                const isRouteMatch = pathname === targetRoute;\n                if (isRouteMatch) {\n                    safeLog(\"✅ [OPTIMISTIC NAV] Navigation completed: \".concat(targetRoute, \" -> \").concat(pathname));\n                    // Clear timeout if navigation completed successfully\n                    if (timeoutRef.current) {\n                        clearTimeout(timeoutRef.current);\n                        timeoutRef.current = null;\n                    }\n                    // Clear navigation state immediately\n                    setIsNavigating(false);\n                    setTargetRoute(null);\n                    currentNavigationIdRef.current = null;\n                    // Clear any remaining queued navigations for this route\n                    navigationQueueRef.current = navigationQueueRef.current.filter({\n                        \"NavigationProvider.useEffect\": (item)=>item.route !== targetRoute\n                    }[\"NavigationProvider.useEffect\"]);\n                }\n            }\n        }\n    }[\"NavigationProvider.useEffect\"], [\n        pathname,\n        targetRoute,\n        safeLog\n    ]);\n    // Additional immediate route change detection\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NavigationProvider.useEffect\": ()=>{\n            // If we're navigating but the pathname has already changed to match target\n            if (isNavigating && targetRoute && pathname === targetRoute) {\n                safeLog(\"\\uD83D\\uDE80 [OPTIMISTIC NAV] Immediate route match detected, clearing navigation state\");\n                setIsNavigating(false);\n                setTargetRoute(null);\n                if (timeoutRef.current) {\n                    clearTimeout(timeoutRef.current);\n                    timeoutRef.current = null;\n                }\n            }\n        }\n    }[\"NavigationProvider.useEffect\"], [\n        pathname,\n        targetRoute,\n        isNavigating,\n        safeLog\n    ]);\n    // Check if page is cached (visited before)\n    const isPageCached = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NavigationProvider.useCallback[isPageCached]\": (route)=>{\n            return cachedPages.has(route);\n        }\n    }[\"NavigationProvider.useCallback[isPageCached]\"], [\n        cachedPages\n    ]);\n    // Process navigation queue\n    const processNavigationQueue = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NavigationProvider.useCallback[processNavigationQueue]\": ()=>{\n            if (navigationQueueRef.current.length === 0) return;\n            // Get the most recent navigation request\n            const latestNavigation = navigationQueueRef.current[navigationQueueRef.current.length - 1];\n            // Clear all older navigation requests\n            navigationQueueRef.current = [\n                latestNavigation\n            ];\n            const { route, id } = latestNavigation;\n            safeLog(\"\\uD83D\\uDE80 [OPTIMISTIC NAV] Processing navigation to: \".concat(route, \" (id: \").concat(id, \")\"));\n            // Clear any existing timeout\n            if (timeoutRef.current) {\n                clearTimeout(timeoutRef.current);\n                timeoutRef.current = null;\n            }\n            // Update navigation ID and confirm target route\n            currentNavigationIdRef.current = id;\n            // Target route should already be set by navigateOptimistically\n            // For cached pages, show minimal loading\n            const isCached = isPageCached(route);\n            if (isCached) {\n                safeLog(\"⚡ [OPTIMISTIC NAV] Using cached navigation for: \".concat(route));\n                // Clear quickly for cached pages\n                setTimeout({\n                    \"NavigationProvider.useCallback[processNavigationQueue]\": ()=>{\n                        if (currentNavigationIdRef.current === id) {\n                            setIsNavigating(false);\n                        }\n                    }\n                }[\"NavigationProvider.useCallback[processNavigationQueue]\"], 100);\n            }\n            // Start actual navigation immediately\n            try {\n                router.push(route);\n            } catch (error) {\n                safeLog(\"❌ [OPTIMISTIC NAV] Router.push failed for: \".concat(route, \", using fallback\"));\n                // Fallback to window.location if router fails\n                window.location.href = route;\n                return;\n            }\n            // Set timeout for fallback (shorter for cached pages, but more generous)\n            const timeoutDuration = isCached ? 800 : 3000;\n            timeoutRef.current = setTimeout({\n                \"NavigationProvider.useCallback[processNavigationQueue]\": ()=>{\n                    safeLog(\"⚠️ [OPTIMISTIC NAV] Timeout reached for: \".concat(route, \" (id: \").concat(id, \"), current path: \").concat(pathname));\n                    if (currentNavigationIdRef.current === id) {\n                        // Try fallback navigation\n                        safeLog(\"\\uD83D\\uDD04 [OPTIMISTIC NAV] Attempting fallback navigation to: \".concat(route));\n                        try {\n                            window.location.href = route;\n                        } catch (fallbackError) {\n                            safeLog(\"❌ [OPTIMISTIC NAV] Fallback navigation failed: \".concat(fallbackError));\n                        }\n                        setIsNavigating(false);\n                        setTargetRoute(null);\n                        currentNavigationIdRef.current = null;\n                    }\n                    timeoutRef.current = null;\n                }\n            }[\"NavigationProvider.useCallback[processNavigationQueue]\"], timeoutDuration);\n        }\n    }[\"NavigationProvider.useCallback[processNavigationQueue]\"], [\n        router,\n        pathname,\n        isPageCached,\n        safeLog\n    ]);\n    const navigateOptimistically = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NavigationProvider.useCallback[navigateOptimistically]\": (href)=>{\n            // Don't navigate if we're already on this exact route\n            if (pathname === href || !isClient) {\n                return;\n            }\n            const now = Date.now();\n            // Simple debounce to prevent rapid clicks\n            if (now - lastNavigationTimeRef.current < 100 && targetRoute === href) {\n                safeLog(\"\\uD83D\\uDD04 [OPTIMISTIC NAV] Debouncing duplicate navigation to: \".concat(href));\n                return;\n            }\n            lastNavigationTimeRef.current = now;\n            // Track click count for escape hatch\n            if (!clickCountRef.current[href]) {\n                clickCountRef.current[href] = 0;\n            }\n            clickCountRef.current[href]++;\n            // Clear click count after 2 seconds\n            if (clickTimeoutRef.current[href]) {\n                clearTimeout(clickTimeoutRef.current[href]);\n            }\n            clickTimeoutRef.current[href] = setTimeout({\n                \"NavigationProvider.useCallback[navigateOptimistically]\": ()=>{\n                    clickCountRef.current[href] = 0;\n                }\n            }[\"NavigationProvider.useCallback[navigateOptimistically]\"], 2000);\n            // If user clicks same route 3+ times quickly, force regular navigation\n            if (clickCountRef.current[href] >= 3) {\n                safeLog(\"\\uD83D\\uDEA8 [OPTIMISTIC NAV] Force navigation escape hatch for: \".concat(href));\n                clickCountRef.current[href] = 0;\n                if (true) {\n                    window.location.href = href;\n                }\n                return;\n            }\n            // Clear any existing navigation state first\n            if (timeoutRef.current) {\n                clearTimeout(timeoutRef.current);\n                timeoutRef.current = null;\n            }\n            // IMMEDIATE visual feedback - set loading state right away\n            setIsNavigating(true);\n            setTargetRoute(href);\n            // Generate unique ID for this navigation\n            const navigationId = \"nav_\".concat(now, \"_\").concat(Math.random().toString(36).substr(2, 9));\n            // Clear queue and add new navigation\n            navigationQueueRef.current = [\n                {\n                    route: href,\n                    timestamp: now,\n                    id: navigationId\n                }\n            ];\n            // Process queue immediately for instant feedback\n            processNavigationQueue();\n        }\n    }[\"NavigationProvider.useCallback[navigateOptimistically]\"], [\n        pathname,\n        targetRoute,\n        processNavigationQueue,\n        safeLog,\n        isClient\n    ]);\n    const clearNavigation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NavigationProvider.useCallback[clearNavigation]\": ()=>{\n            if (timeoutRef.current) {\n                clearTimeout(timeoutRef.current);\n                timeoutRef.current = null;\n            }\n            setIsNavigating(false);\n            setTargetRoute(null);\n            currentNavigationIdRef.current = null;\n            navigationQueueRef.current = [];\n        }\n    }[\"NavigationProvider.useCallback[clearNavigation]\"], []);\n    // Additional safety: clear navigation state when document becomes visible\n    // This handles cases where the route change detection might miss\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NavigationProvider.useEffect\": ()=>{\n            // Only add event listener on client side\n            if (!isClient || \"object\" === 'undefined') return;\n            const handleVisibilityChange = {\n                \"NavigationProvider.useEffect.handleVisibilityChange\": ()=>{\n                    if (!document.hidden && isNavigating) {\n                        safeLog(\"\\uD83D\\uDC41️ [OPTIMISTIC NAV] Document visible, checking if navigation should clear\");\n                        // Small delay to allow route to update\n                        setTimeout({\n                            \"NavigationProvider.useEffect.handleVisibilityChange\": ()=>{\n                                if (targetRoute) {\n                                    const isRouteMatch = pathname === targetRoute;\n                                    if (isRouteMatch) {\n                                        safeLog(\"\\uD83D\\uDD27 [OPTIMISTIC NAV] Force clearing navigation state\");\n                                        setIsNavigating(false);\n                                        setTargetRoute(null);\n                                        if (timeoutRef.current) {\n                                            clearTimeout(timeoutRef.current);\n                                            timeoutRef.current = null;\n                                        }\n                                    }\n                                }\n                            }\n                        }[\"NavigationProvider.useEffect.handleVisibilityChange\"], 100);\n                    }\n                }\n            }[\"NavigationProvider.useEffect.handleVisibilityChange\"];\n            document.addEventListener('visibilitychange', handleVisibilityChange);\n            return ({\n                \"NavigationProvider.useEffect\": ()=>document.removeEventListener('visibilitychange', handleVisibilityChange)\n            })[\"NavigationProvider.useEffect\"];\n        }\n    }[\"NavigationProvider.useEffect\"], [\n        isNavigating,\n        targetRoute,\n        pathname,\n        safeLog,\n        isClient\n    ]);\n    // Cleanup timeout on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NavigationProvider.useEffect\": ()=>{\n            return ({\n                \"NavigationProvider.useEffect\": ()=>{\n                    if (timeoutRef.current) {\n                        clearTimeout(timeoutRef.current);\n                    }\n                }\n            })[\"NavigationProvider.useEffect\"];\n        }\n    }[\"NavigationProvider.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavigationContext.Provider, {\n        value: {\n            isNavigating,\n            targetRoute,\n            navigateOptimistically,\n            clearNavigation,\n            isPageCached,\n            navigationHistory\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\contexts\\\\NavigationContext.tsx\",\n        lineNumber: 290,\n        columnNumber: 5\n    }, this);\n}\n_s(NavigationProvider, \"vATWKAiHgWda+aWoMsrxP4ttJDk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = NavigationProvider;\nfunction useNavigation() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(NavigationContext);\n    if (context === undefined) {\n        throw new Error('useNavigation must be used within a NavigationProvider');\n    }\n    return context;\n}\n_s1(useNavigation, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n// Safe version that returns null instead of throwing\nfunction useNavigationSafe() {\n    _s2();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(NavigationContext);\n    return context || null;\n}\n_s2(useNavigationSafe, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"NavigationProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/NavigationContext.tsx\n"));

/***/ })

});