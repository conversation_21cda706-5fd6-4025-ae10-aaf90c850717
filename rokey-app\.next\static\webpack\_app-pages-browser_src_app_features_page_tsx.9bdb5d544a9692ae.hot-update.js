"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_app_features_page_tsx",{

/***/ "(app-pages-browser)/./src/app/features/page.tsx":
/*!***********************************!*\
  !*** ./src/app/features/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FeaturesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,CloudIcon,CodeBracketIcon,CogIcon,CpuChipIcon,LightBulbIcon,RocketLaunchIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,CloudIcon,CodeBracketIcon,CogIcon,CpuChipIcon,LightBulbIcon,RocketLaunchIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,CloudIcon,CodeBracketIcon,CogIcon,CpuChipIcon,LightBulbIcon,RocketLaunchIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,CloudIcon,CodeBracketIcon,CogIcon,CpuChipIcon,LightBulbIcon,RocketLaunchIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,CloudIcon,CodeBracketIcon,CogIcon,CpuChipIcon,LightBulbIcon,RocketLaunchIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowPathIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,CloudIcon,CodeBracketIcon,CogIcon,CpuChipIcon,LightBulbIcon,RocketLaunchIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CloudIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,CloudIcon,CodeBracketIcon,CogIcon,CpuChipIcon,LightBulbIcon,RocketLaunchIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CodeBracketIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,CloudIcon,CodeBracketIcon,CogIcon,CpuChipIcon,LightBulbIcon,RocketLaunchIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/LightBulbIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,CloudIcon,CodeBracketIcon,CogIcon,CpuChipIcon,LightBulbIcon,RocketLaunchIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/RocketLaunchIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,ChartBarIcon,CloudIcon,CodeBracketIcon,CogIcon,CpuChipIcon,LightBulbIcon,RocketLaunchIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CogIcon.js\");\n/* harmony import */ var _components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/landing/LandingNavbar */ \"(app-pages-browser)/./src/components/landing/LandingNavbar.tsx\");\n/* harmony import */ var _hooks_usePerformanceOptimization__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/usePerformanceOptimization */ \"(app-pages-browser)/./src/hooks/usePerformanceOptimization.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n// Lazy load footer for better initial load\nconst Footer = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.lazy)(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_landing_Footer_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/landing/Footer */ \"(app-pages-browser)/./src/components/landing/Footer.tsx\")));\n_c = Footer;\nconst features = [\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: \"Intelligent Routing\",\n        description: \"Advanced AI algorithms analyze your prompts and automatically route them to the optimal model for best results.\",\n        details: [\n            \"Real-time prompt analysis\",\n            \"Context-aware routing decisions\",\n            \"Performance optimization\",\n            \"Cost-effective model selection\"\n        ]\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        title: \"Enterprise Security\",\n        description: \"Bank-grade security with end-to-end encryption, compliance certifications, and audit trails.\",\n        details: [\n            \"SOC 2 Type II compliance\",\n            \"End-to-end encryption\",\n            \"Complete audit trails\",\n            \"Role-based access control\"\n        ]\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        title: \"Advanced Analytics\",\n        description: \"Comprehensive insights into model performance, costs, and usage patterns with real-time dashboards.\",\n        details: [\n            \"Real-time performance metrics\",\n            \"Cost optimization insights\",\n            \"Usage pattern analysis\",\n            \"Custom reporting\"\n        ]\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        title: \"300+ AI Models\",\n        description: \"Access to the largest collection of AI models from leading providers, all through one unified API.\",\n        details: [\n            \"OpenAI, Anthropic, Google, Meta\",\n            \"Specialized domain models\",\n            \"Latest model versions\",\n            \"Custom model integration\"\n        ]\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        title: \"Auto-Failover\",\n        description: \"Automatic failover and retry mechanisms ensure 99.9% uptime for your AI applications.\",\n        details: [\n            \"Intelligent failover routing\",\n            \"Automatic retry logic\",\n            \"Health monitoring\",\n            \"Zero-downtime switching\"\n        ]\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        title: \"Global Infrastructure\",\n        description: \"Distributed infrastructure across multiple regions for low latency and high availability.\",\n        details: [\n            \"Multi-region deployment\",\n            \"Edge computing optimization\",\n            \"CDN acceleration\",\n            \"99.9% uptime SLA\"\n        ]\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        title: \"Developer-First\",\n        description: \"Built by developers, for developers. Simple APIs, comprehensive docs, and powerful SDKs.\",\n        details: [\n            \"RESTful API design\",\n            \"Multiple SDK languages\",\n            \"Interactive documentation\",\n            \"Code examples & tutorials\"\n        ]\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        title: \"Smart Optimization\",\n        description: \"Continuous learning algorithms optimize routing decisions based on your usage patterns.\",\n        details: [\n            \"Machine learning optimization\",\n            \"Pattern recognition\",\n            \"Adaptive routing\",\n            \"Performance tuning\"\n        ]\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        title: \"Rapid Deployment\",\n        description: \"Get started in minutes with our simple integration process and migration tools.\",\n        details: [\n            \"5-minute setup\",\n            \"Migration assistance\",\n            \"Zero-code integration\",\n            \"Instant activation\"\n        ]\n    },\n    {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_ChartBarIcon_CloudIcon_CodeBracketIcon_CogIcon_CpuChipIcon_LightBulbIcon_RocketLaunchIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        title: \"Custom Configurations\",\n        description: \"Flexible routing rules and custom configurations to match your specific requirements.\",\n        details: [\n            \"Custom routing logic\",\n            \"Business rule engine\",\n            \"A/B testing support\",\n            \"Configuration templates\"\n        ]\n    }\n];\nfunction FeaturesPage() {\n    _s();\n    const { startMeasurement, endMeasurement } = (0,_hooks_usePerformanceOptimization__WEBPACK_IMPORTED_MODULE_3__.usePerformanceOptimization)('FeaturesPage', {\n        enableMonitoring: true,\n        enableMemoryTracking: true,\n        warningThresholds: {\n            renderTime: 200,\n            memoryUsage: 40 * 1024 * 1024 // 40MB\n        }\n    });\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"FeaturesPage.useEffect\": ()=>{\n            startMeasurement();\n            return ({\n                \"FeaturesPage.useEffect\": ()=>endMeasurement()\n            })[\"FeaturesPage.useEffect\"];\n        }\n    }[\"FeaturesPage.useEffect\"], [\n        startMeasurement,\n        endMeasurement\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                lineNumber: 153,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"pt-20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20 bg-gradient-to-br from-slate-50 to-blue-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-5xl md:text-6xl font-bold text-gray-900 mb-6\",\n                                        children: [\n                                            \"Powerful Features for\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-[#ff6b35] block\",\n                                                children: \"Modern AI Development\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-gray-600 max-w-3xl mx-auto mb-8\",\n                                        children: \"Everything you need to build, deploy, and scale AI applications with confidence. From intelligent routing to enterprise security, we've got you covered.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-12\",\n                                children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.6,\n                                            delay: index * 0.1\n                                        },\n                                        className: \"bg-white rounded-2xl p-8 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-12 h-12 bg-[#ff6b35] rounded-xl flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                            className: \"w-6 h-6 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                                            lineNumber: 191,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                                        lineNumber: 190,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                                    lineNumber: 189,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-semibold text-gray-900 mb-3\",\n                                                            children: feature.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                                            lineNumber: 195,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 mb-4\",\n                                                            children: feature.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                                            lineNumber: 198,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"space-y-2\",\n                                                            children: feature.details.map((detail, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    className: \"flex items-center text-sm text-gray-500\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-1.5 h-1.5 bg-[#ff6b35] rounded-full mr-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                                                            lineNumber: 204,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        detail\n                                                                    ]\n                                                                }, idx, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                                                    lineNumber: 203,\n                                                                    columnNumber: 27\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                                            lineNumber: 201,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, feature.title, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20 bg-gray-900\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-4xl font-bold text-white mb-6\",\n                                        children: \"Ready to Transform Your AI Development?\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-gray-300 mb-8\",\n                                        children: \"Join thousands of developers who trust RouKey for their AI infrastructure.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.button, {\n                                        whileHover: {\n                                            scale: 1.05\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        className: \"bg-[#ff6b35] text-white px-8 py-4 rounded-xl font-semibold text-lg hover:bg-[#e55a2b] transition-colors duration-200 shadow-lg\",\n                                        children: \"Get Started Now\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                lineNumber: 155,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n                fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-32 bg-gray-900\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                    lineNumber: 243,\n                    columnNumber: 27\n                }, void 0),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Footer, {}, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                    lineNumber: 244,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n                lineNumber: 243,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\",\n        lineNumber: 152,\n        columnNumber: 5\n    }, this);\n}\n_s(FeaturesPage, \"Q+OT+PfUl/epZO1+DioOG7h1YRE=\", false, function() {\n    return [\n        _hooks_usePerformanceOptimization__WEBPACK_IMPORTED_MODULE_3__.usePerformanceOptimization\n    ];\n});\n_c1 = FeaturesPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"Footer\");\n$RefreshReg$(_c1, \"FeaturesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/features/page.tsx\n"));

/***/ })

});