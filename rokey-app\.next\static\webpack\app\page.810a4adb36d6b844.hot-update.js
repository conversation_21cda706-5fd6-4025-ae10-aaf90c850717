"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/OptimisticLink.tsx":
/*!*******************************************!*\
  !*** ./src/components/OptimisticLink.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OptimisticButton: () => (/* binding */ OptimisticButton),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useOptimisticNavigation: () => (/* binding */ useOptimisticNavigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/NavigationContext */ \"(app-pages-browser)/./src/contexts/NavigationContext.tsx\");\n/* harmony import */ var _utils_cacheStrategy__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/cacheStrategy */ \"(app-pages-browser)/./src/utils/cacheStrategy.ts\");\n/* __next_internal_client_entry_do_not_use__ default,OptimisticButton,useOptimisticNavigation auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\nconst OptimisticLink = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(_c = (param, ref)=>{\n    let { href, children, className = '', prefetch = true, onClick, onMouseEnter, ...props } = param;\n    // Use try-catch to handle cases where NavigationProvider might not be available\n    let navigationContext;\n    try {\n        navigationContext = (0,_contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_3__.useNavigation)();\n    } catch (error) {\n        // Fallback to regular Link behavior if NavigationProvider is not available\n        console.warn('NavigationProvider not found, falling back to regular Link behavior');\n        navigationContext = null;\n    }\n    const { navigateOptimistically, isNavigating, targetRoute } = navigationContext || {\n        navigateOptimistically: ()=>{},\n        isNavigating: false,\n        targetRoute: null\n    };\n    const handleClick = (e)=>{\n        // Don't prevent default for external links or special cases\n        if (href.startsWith('http') || href.startsWith('mailto:') || href.startsWith('tel:')) {\n            onClick === null || onClick === void 0 ? void 0 : onClick(e);\n            return;\n        }\n        // Don't prevent default if user is holding modifier keys\n        if (e.metaKey || e.ctrlKey || e.shiftKey || e.altKey) {\n            onClick === null || onClick === void 0 ? void 0 : onClick(e);\n            return;\n        }\n        // Prevent default navigation and use optimistic loading\n        e.preventDefault();\n        // Call custom onClick if provided\n        onClick === null || onClick === void 0 ? void 0 : onClick(e);\n        // Start optimistic navigation\n        navigateOptimistically(href);\n    };\n    const handleMouseEnter = (e)=>{\n        // Prefetch on hover for better performance\n        if (prefetch && href.startsWith('/')) {\n            _utils_cacheStrategy__WEBPACK_IMPORTED_MODULE_4__.prefetcher.schedulePrefetch(href);\n        }\n        // Call custom onMouseEnter if provided\n        onMouseEnter === null || onMouseEnter === void 0 ? void 0 : onMouseEnter(e);\n    };\n    // Add loading state to className if this link is being navigated to\n    const isCurrentlyNavigating = isNavigating && targetRoute === href;\n    const finalClassName = \"\".concat(className, \" \").concat(isCurrentlyNavigating ? 'optimistic-loading' : '').trim();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n        ref: ref,\n        href: href,\n        className: finalClassName,\n        onClick: handleClick,\n        onMouseEnter: handleMouseEnter,\n        prefetch: prefetch,\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticLink.tsx\",\n        lineNumber: 90,\n        columnNumber: 7\n    }, undefined);\n});\n_c1 = OptimisticLink;\nOptimisticLink.displayName = 'OptimisticLink';\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (OptimisticLink);\nconst OptimisticButton = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(_c2 = _s((param, ref)=>{\n    let { href, children, className = '', onClick, onMouseEnter, disabled = false, type = 'button', ...props } = param;\n    _s();\n    const { navigateOptimistically, isNavigating, targetRoute } = (0,_contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_3__.useNavigation)();\n    const handleClick = (e)=>{\n        if (disabled) return;\n        // Call custom onClick first\n        onClick === null || onClick === void 0 ? void 0 : onClick(e);\n        // If href is provided, navigate optimistically\n        if (href && href.startsWith('/')) {\n            navigateOptimistically(href);\n        }\n    };\n    const handleMouseEnter = (e)=>{\n        // Prefetch on hover if href is provided\n        if (href && href.startsWith('/')) {\n            _utils_cacheStrategy__WEBPACK_IMPORTED_MODULE_4__.prefetcher.schedulePrefetch(href);\n        }\n        // Call custom onMouseEnter if provided\n        onMouseEnter === null || onMouseEnter === void 0 ? void 0 : onMouseEnter(e);\n    };\n    // Add loading state to className if this button's href is being navigated to\n    const isCurrentlyNavigating = isNavigating && targetRoute === href;\n    const finalClassName = \"\".concat(className, \" \").concat(isCurrentlyNavigating ? 'optimistic-loading' : '', \" \").concat(disabled ? 'opacity-50 cursor-not-allowed' : '').trim();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        ref: ref,\n        type: type,\n        className: finalClassName,\n        onClick: handleClick,\n        onMouseEnter: handleMouseEnter,\n        disabled: disabled,\n        ...props,\n        children: [\n            children,\n            isCurrentlyNavigating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"ml-2 inline-block w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticLink.tsx\",\n                lineNumber: 175,\n                columnNumber: 11\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OptimisticLink.tsx\",\n        lineNumber: 164,\n        columnNumber: 7\n    }, undefined);\n}, \"6iadbAj6+23p8LPCxYTKXwxcagM=\", false, function() {\n    return [\n        _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_3__.useNavigation\n    ];\n})), \"6iadbAj6+23p8LPCxYTKXwxcagM=\", false, function() {\n    return [\n        _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_3__.useNavigation\n    ];\n});\n_c3 = OptimisticButton;\nOptimisticButton.displayName = 'OptimisticButton';\n// Hook for programmatic optimistic navigation\nfunction useOptimisticNavigation() {\n    _s1();\n    const { navigateOptimistically, isNavigating, targetRoute, isPageCached } = (0,_contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_3__.useNavigation)();\n    const navigate = (href)=>{\n        if (href.startsWith('/')) {\n            navigateOptimistically(href);\n        } else {\n            // For external links, use regular navigation\n            window.location.href = href;\n        }\n    };\n    const isNavigatingTo = (href)=>{\n        return isNavigating && targetRoute === href;\n    };\n    const isCached = (href)=>{\n        return isPageCached(href);\n    };\n    return {\n        navigate,\n        isNavigating,\n        targetRoute,\n        isNavigatingTo,\n        isCached\n    };\n}\n_s1(useOptimisticNavigation, \"L8/PZCoRzd5hsXKrUXO+HIANQpk=\", false, function() {\n    return [\n        _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_3__.useNavigation\n    ];\n});\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"OptimisticLink$forwardRef\");\n$RefreshReg$(_c1, \"OptimisticLink\");\n$RefreshReg$(_c2, \"OptimisticButton$forwardRef\");\n$RefreshReg$(_c3, \"OptimisticButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/OptimisticLink.tsx\n"));

/***/ })

});