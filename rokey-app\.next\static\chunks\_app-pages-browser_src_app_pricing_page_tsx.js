"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_app_pricing_page_tsx"],{

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckIcon.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/CheckIcon.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction CheckIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"m4.5 12.75 6 6 9-13.5\"\n    }));\n}\n_c = CheckIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(CheckIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"CheckIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/StarIcon.js":
/*!******************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/StarIcon.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction StarIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M11.48 3.499a.562.562 0 0 1 1.04 0l2.125 5.111a.563.563 0 0 0 .475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 0 0-.182.557l1.285 5.385a.562.562 0 0 1-.84.61l-4.725-2.885a.562.562 0 0 0-.586 0L6.982 20.54a.562.562 0 0 1-.84-.61l1.285-5.386a.562.562 0 0 0-.182-.557l-4.204-3.602a.562.562 0 0 1 .321-.988l5.518-.442a.563.563 0 0 0 .475-.345L11.48 3.5Z\"\n    }));\n}\n_c = StarIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(StarIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"StarIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/StarIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/pricing/page.tsx":
/*!**********************************!*\
  !*** ./src/app/pricing/page.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PricingPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/StarIcon.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/landing/LandingNavbar */ \"(app-pages-browser)/./src/components/landing/LandingNavbar.tsx\");\n/* harmony import */ var _hooks_usePerformanceOptimization__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/usePerformanceOptimization */ \"(app-pages-browser)/./src/hooks/usePerformanceOptimization.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// Lazy load footer for better initial load\nconst Footer = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.lazy)(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_landing_Footer_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/landing/Footer */ \"(app-pages-browser)/./src/components/landing/Footer.tsx\")));\n_c = Footer;\nconst pricingTiers = [\n    {\n        name: \"Starter\",\n        price: 29,\n        description: \"Perfect for individual developers and small projects\",\n        features: [\n            \"Unlimited API requests\",\n            \"3 Custom Configurations\",\n            \"10 API Keys per config\",\n            \"All 300+ AI models\",\n            \"Intelligent role routing\",\n            \"Basic analytics (30-day history)\",\n            \"Community support\"\n        ],\n        notIncluded: [\n            \"Advanced routing strategies\",\n            \"Performance monitoring\",\n            \"Priority support\"\n        ],\n        cta: \"Get Started\",\n        popular: false\n    },\n    {\n        name: \"Professional\",\n        price: 99,\n        description: \"Ideal for growing businesses and development teams\",\n        features: [\n            \"Unlimited API requests\",\n            \"15 Custom Configurations\",\n            \"50 API Keys per config\",\n            \"All 300+ AI models\",\n            \"Advanced routing strategies\",\n            \"Advanced analytics (90-day history)\",\n            \"Performance monitoring\",\n            \"Cost optimization alerts\",\n            \"Priority email support\"\n        ],\n        notIncluded: [\n            \"Custom routing rules\",\n            \"Enterprise analytics\",\n            \"Dedicated support\"\n        ],\n        cta: \"Get Started\",\n        popular: true\n    },\n    {\n        name: \"Enterprise\",\n        price: 299,\n        description: \"For large organizations with high-volume needs\",\n        features: [\n            \"Unlimited API requests\",\n            \"Unlimited configurations\",\n            \"Unlimited API keys\",\n            \"All 300+ models + priority access\",\n            \"Custom routing rules\",\n            \"Enterprise analytics (1-year history)\",\n            \"Advanced SLA monitoring\",\n            \"Team management (coming soon)\",\n            \"Dedicated support + phone\"\n        ],\n        notIncluded: [],\n        cta: \"Get Started\",\n        popular: false\n    }\n];\nconst comparisonFeatures = [\n    {\n        category: \"Usage Limits\",\n        features: [\n            {\n                name: \"API Requests per month\",\n                starter: \"Unlimited\",\n                pro: \"Unlimited\",\n                enterprise: \"Unlimited\"\n            },\n            {\n                name: \"Custom Configurations\",\n                starter: \"3\",\n                pro: \"15\",\n                enterprise: \"Unlimited\"\n            },\n            {\n                name: \"API Keys per config\",\n                starter: \"10\",\n                pro: \"50\",\n                enterprise: \"Unlimited\"\n            }\n        ]\n    },\n    {\n        category: \"AI Models & Routing\",\n        features: [\n            {\n                name: \"Supported AI Models\",\n                starter: \"300+\",\n                pro: \"300+\",\n                enterprise: \"300+ + Priority\"\n            },\n            {\n                name: \"Intelligent Role Routing\",\n                starter: \"✓\",\n                pro: \"✓\",\n                enterprise: \"✓\",\n                type: \"check\"\n            },\n            {\n                name: \"Advanced Routing Strategies\",\n                starter: \"✗\",\n                pro: \"✓\",\n                enterprise: \"✓\",\n                type: \"mixed\"\n            },\n            {\n                name: \"Custom Routing Rules\",\n                starter: \"✗\",\n                pro: \"✗\",\n                enterprise: \"✓\",\n                type: \"mixed\"\n            }\n        ]\n    },\n    {\n        category: \"Analytics & Monitoring\",\n        features: [\n            {\n                name: \"Basic Analytics\",\n                starter: \"30 days\",\n                pro: \"90 days\",\n                enterprise: \"1 year\"\n            },\n            {\n                name: \"Performance Monitoring\",\n                starter: \"✗\",\n                pro: \"✓\",\n                enterprise: \"✓\",\n                type: \"mixed\"\n            },\n            {\n                name: \"Cost Optimization\",\n                starter: \"Basic\",\n                pro: \"Advanced\",\n                enterprise: \"Enterprise\"\n            },\n            {\n                name: \"SLA Monitoring\",\n                starter: \"✗\",\n                pro: \"✗\",\n                enterprise: \"✓\",\n                type: \"mixed\"\n            }\n        ]\n    },\n    {\n        category: \"Support & Management\",\n        features: [\n            {\n                name: \"Support Level\",\n                starter: \"Community\",\n                pro: \"Priority Email\",\n                enterprise: \"Dedicated + Phone\"\n            },\n            {\n                name: \"Team Management\",\n                starter: \"✗\",\n                pro: \"✗\",\n                enterprise: \"✓\",\n                type: \"mixed\"\n            },\n            {\n                name: \"Custom Integrations\",\n                starter: \"✗\",\n                pro: \"✗\",\n                enterprise: \"✓\",\n                type: \"mixed\"\n            }\n        ]\n    }\n];\n// Helper function to render feature values with proper styling\nconst renderFeatureValue = (value)=>{\n    if (value === \"✓\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-5 w-5 text-green-500 mx-auto\"\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n            lineNumber: 119,\n            columnNumber: 12\n        }, undefined);\n    }\n    if (value === \"✗\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            className: \"h-5 w-5 text-red-500 mx-auto\"\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n            lineNumber: 122,\n            columnNumber: 12\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: \"text-gray-700\",\n        children: value\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n        lineNumber: 124,\n        columnNumber: 10\n    }, undefined);\n};\nfunction PricingPage() {\n    _s();\n    const { startMeasurement, endMeasurement, metrics } = (0,_hooks_usePerformanceOptimization__WEBPACK_IMPORTED_MODULE_4__.usePerformanceOptimization)('PricingPage', {\n        enableMonitoring: true,\n        enableMemoryTracking: true,\n        warningThresholds: {\n            renderTime: 150,\n            memoryUsage: 30 * 1024 * 1024 // 30MB\n        }\n    });\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"PricingPage.useEffect\": ()=>{\n            startMeasurement();\n            return ({\n                \"PricingPage.useEffect\": ()=>endMeasurement()\n            })[\"PricingPage.useEffect\"];\n        }\n    }[\"PricingPage.useEffect\"], [\n        startMeasurement,\n        endMeasurement\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                lineNumber: 144,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"pt-20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 bg-gradient-to-br from-slate-50 to-blue-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.h1, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    className: \"text-4xl sm:text-5xl font-bold text-gray-900 mb-6\",\n                                    children: \"Simple, Transparent Pricing\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.p, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        delay: 0.1\n                                    },\n                                    className: \"text-xl text-gray-600 max-w-3xl mx-auto mb-8\",\n                                    children: \"Choose the perfect plan for your needs. All plans include unlimited access to 300+ AI models with intelligent routing. You only pay for your own API usage - no hidden fees, no surprises.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        delay: 0.2\n                                    },\n                                    className: \"inline-flex items-center px-4 py-2 rounded-full bg-[#ff6b35]/10 text-[#ff6b35] text-sm font-medium\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Instant activation • Start building immediately\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-6xl mx-auto\",\n                                children: pricingTiers.map((tier, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            delay: index * 0.1\n                                        },\n                                        className: \"relative bg-white rounded-2xl border-2 p-8 \".concat(tier.popular ? 'border-[#ff6b35] shadow-xl scale-105' : 'border-gray-200 shadow-sm'),\n                                        children: [\n                                            tier.popular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -top-4 left-1/2 transform -translate-x-1/2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-[#ff6b35] text-white px-4 py-1 rounded-full text-sm font-medium flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                            lineNumber: 198,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        \"Most Popular\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center mb-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                                        children: tier.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                        lineNumber: 205,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 mb-4\",\n                                                        children: tier.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                        lineNumber: 206,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-baseline justify-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-4xl font-bold text-gray-900\",\n                                                                children: [\n                                                                    \"$\",\n                                                                    tier.price\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                lineNumber: 208,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-600 ml-2\",\n                                                                children: \"/month\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                lineNumber: 209,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                        lineNumber: 207,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4 mb-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold text-gray-900 mb-3\",\n                                                                children: \"Included:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                lineNumber: 215,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                className: \"space-y-2\",\n                                                                children: tier.features.map((feature, featureIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        className: \"flex items-start\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                                className: \"h-5 w-5 text-green-500 mr-3 mt-0.5 flex-shrink-0\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                                lineNumber: 219,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-gray-700 text-sm\",\n                                                                                children: feature\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                                lineNumber: 220,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, featureIndex, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                        lineNumber: 218,\n                                                                        columnNumber: 27\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                lineNumber: 216,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                        lineNumber: 214,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    tier.notIncluded.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold text-gray-900 mb-3\",\n                                                                children: \"Not included:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                lineNumber: 228,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                className: \"space-y-2\",\n                                                                children: tier.notIncluded.map((feature, featureIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        className: \"flex items-start\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                                className: \"h-5 w-5 text-gray-400 mr-3 mt-0.5 flex-shrink-0\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                                lineNumber: 232,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-gray-500 text-sm\",\n                                                                                children: feature\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                                lineNumber: 233,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, featureIndex, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                        lineNumber: 231,\n                                                                        columnNumber: 29\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                lineNumber: 229,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                        lineNumber: 227,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/auth/signup\",\n                                                className: \"block w-full text-center py-3 px-6 rounded-lg font-semibold transition-all duration-200 \".concat(tier.popular ? 'bg-[#ff6b35] text-white hover:bg-[#e55a2b] shadow-lg hover:shadow-xl' : 'bg-gray-100 text-gray-900 hover:bg-gray-200'),\n                                                children: tier.cta\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, tier.name, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 bg-gray-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mb-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                            children: \"Detailed Feature Comparison\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl text-gray-600\",\n                                            children: \"Compare all features across our pricing tiers\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"overflow-x-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                            className: \"w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                    className: \"bg-gray-50\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-6 py-4 text-left text-sm font-semibold text-gray-900\",\n                                                                children: \"Features\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                lineNumber: 270,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-6 py-4 text-center text-sm font-semibold text-gray-900\",\n                                                                children: \"Starter\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                lineNumber: 271,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-6 py-4 text-center text-sm font-semibold text-gray-900 bg-[#ff6b35]/5\",\n                                                                children: \"Professional\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                lineNumber: 272,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-6 py-4 text-center text-sm font-semibold text-gray-900\",\n                                                                children: \"Enterprise\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                lineNumber: 273,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                    className: \"divide-y divide-gray-200\",\n                                                    children: comparisonFeatures.map((category, categoryIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                    className: \"bg-gray-50\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        colSpan: 4,\n                                                                        className: \"px-6 py-3 text-sm font-semibold text-gray-900\",\n                                                                        children: category.category\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                        lineNumber: 280,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                    lineNumber: 279,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                category.features.map((feature, featureIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                        className: \"hover:bg-gray-50\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"px-6 py-4 text-sm text-gray-900\",\n                                                                                children: feature.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                                lineNumber: 286,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"px-6 py-4 text-sm text-center\",\n                                                                                children: renderFeatureValue(feature.starter)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                                lineNumber: 287,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"px-6 py-4 text-sm text-center bg-[#ff6b35]/5\",\n                                                                                children: renderFeatureValue(feature.pro)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                                lineNumber: 288,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"px-6 py-4 text-sm text-center\",\n                                                                                children: renderFeatureValue(feature.enterprise)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                                lineNumber: 289,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, \"\".concat(categoryIndex, \"-\").concat(featureIndex), true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                                        lineNumber: 285,\n                                                                        columnNumber: 27\n                                                                    }, this))\n                                                            ]\n                                                        }, category.category, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                            lineNumber: 278,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                            lineNumber: 259,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                        lineNumber: 258,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                lineNumber: 146,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n                fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-32 bg-gray-900\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                    lineNumber: 302,\n                    columnNumber: 27\n                }, void 0),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Footer, {}, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                    lineNumber: 303,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n                lineNumber: 302,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\pricing\\\\page.tsx\",\n        lineNumber: 143,\n        columnNumber: 5\n    }, this);\n}\n_s(PricingPage, \"GMRe5+Pb/1x0a8e5V1CO5bFe9bw=\", false, function() {\n    return [\n        _hooks_usePerformanceOptimization__WEBPACK_IMPORTED_MODULE_4__.usePerformanceOptimization\n    ];\n});\n_c1 = PricingPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"Footer\");\n$RefreshReg$(_c1, \"PricingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/pricing/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/usePerformanceOptimization.ts":
/*!*************************************************!*\
  !*** ./src/hooks/usePerformanceOptimization.ts ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   usePageLoadPerformance: () => (/* binding */ usePageLoadPerformance),\n/* harmony export */   usePerformanceOptimization: () => (/* binding */ usePerformanceOptimization)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ usePerformanceOptimization,usePageLoadPerformance auto */ \nfunction usePerformanceOptimization(componentName) {\n    let config = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const { enableMonitoring = true, enableMemoryTracking = true, enableBundleAnalysis = false, enableCacheTracking = true, warningThresholds = {\n        renderTime: 100,\n        memoryUsage: 50 * 1024 * 1024,\n        bundleSize: 1024 * 1024 // 1MB\n    } } = config;\n    const [metrics, setMetrics] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        renderTime: 0\n    });\n    const renderStartTime = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    const cacheHits = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    const cacheRequests = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    // Start performance measurement\n    const startMeasurement = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"usePerformanceOptimization.useCallback[startMeasurement]\": ()=>{\n            if (!enableMonitoring) return;\n            renderStartTime.current = performance.now();\n        }\n    }[\"usePerformanceOptimization.useCallback[startMeasurement]\"], [\n        enableMonitoring\n    ]);\n    // End performance measurement\n    const endMeasurement = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"usePerformanceOptimization.useCallback[endMeasurement]\": ()=>{\n            if (!enableMonitoring || !renderStartTime.current) return;\n            const renderTime = performance.now() - renderStartTime.current;\n            setMetrics({\n                \"usePerformanceOptimization.useCallback[endMeasurement]\": (prev)=>({\n                        ...prev,\n                        renderTime\n                    })\n            }[\"usePerformanceOptimization.useCallback[endMeasurement]\"]);\n            // Log warnings if thresholds exceeded\n            if (renderTime > (warningThresholds.renderTime || 100)) {\n                console.warn(\"⚠️ \".concat(componentName, \" slow render: \").concat(renderTime.toFixed(2), \"ms\"));\n            } else if (renderTime < 16) {\n                console.log(\"✅ \".concat(componentName, \" fast render: \").concat(renderTime.toFixed(2), \"ms\"));\n            }\n            renderStartTime.current = 0;\n        }\n    }[\"usePerformanceOptimization.useCallback[endMeasurement]\"], [\n        componentName,\n        enableMonitoring,\n        warningThresholds.renderTime\n    ]);\n    // Memory usage tracking\n    const trackMemoryUsage = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"usePerformanceOptimization.useCallback[trackMemoryUsage]\": ()=>{\n            if (!enableMemoryTracking || !('memory' in performance)) return;\n            const memory = performance.memory;\n            const memoryUsage = {\n                used: memory.usedJSHeapSize,\n                total: memory.totalJSHeapSize,\n                limit: memory.jsHeapSizeLimit\n            };\n            setMetrics({\n                \"usePerformanceOptimization.useCallback[trackMemoryUsage]\": (prev)=>({\n                        ...prev,\n                        memoryUsage\n                    })\n            }[\"usePerformanceOptimization.useCallback[trackMemoryUsage]\"]);\n            // Warning for high memory usage\n            if (memoryUsage.used > (warningThresholds.memoryUsage || 50 * 1024 * 1024)) {\n                console.warn(\"⚠️ High memory usage in \".concat(componentName, \": \").concat((memoryUsage.used / 1024 / 1024).toFixed(2), \"MB\"));\n            }\n        }\n    }[\"usePerformanceOptimization.useCallback[trackMemoryUsage]\"], [\n        componentName,\n        enableMemoryTracking,\n        warningThresholds.memoryUsage\n    ]);\n    // Bundle size analysis\n    const analyzeBundleSize = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"usePerformanceOptimization.useCallback[analyzeBundleSize]\": ()=>{\n            if (!enableBundleAnalysis) return;\n            const resources = performance.getEntriesByType('resource');\n            let totalJSSize = 0;\n            resources.forEach({\n                \"usePerformanceOptimization.useCallback[analyzeBundleSize]\": (resource)=>{\n                    if (resource.name.includes('.js') && resource.transferSize) {\n                        totalJSSize += resource.transferSize;\n                    }\n                }\n            }[\"usePerformanceOptimization.useCallback[analyzeBundleSize]\"]);\n            setMetrics({\n                \"usePerformanceOptimization.useCallback[analyzeBundleSize]\": (prev)=>({\n                        ...prev,\n                        bundleSize: totalJSSize\n                    })\n            }[\"usePerformanceOptimization.useCallback[analyzeBundleSize]\"]);\n            if (totalJSSize > (warningThresholds.bundleSize || 1024 * 1024)) {\n                console.warn(\"⚠️ Large bundle size: \".concat((totalJSSize / 1024 / 1024).toFixed(2), \"MB\"));\n            }\n        }\n    }[\"usePerformanceOptimization.useCallback[analyzeBundleSize]\"], [\n        enableBundleAnalysis,\n        warningThresholds.bundleSize\n    ]);\n    // Cache hit rate tracking\n    const trackCacheHitRate = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"usePerformanceOptimization.useCallback[trackCacheHitRate]\": ()=>{\n            if (!enableCacheTracking) return;\n            const hitRate = cacheRequests.current > 0 ? cacheHits.current / cacheRequests.current * 100 : 0;\n            setMetrics({\n                \"usePerformanceOptimization.useCallback[trackCacheHitRate]\": (prev)=>({\n                        ...prev,\n                        cacheHitRate: hitRate\n                    })\n            }[\"usePerformanceOptimization.useCallback[trackCacheHitRate]\"]);\n        }\n    }[\"usePerformanceOptimization.useCallback[trackCacheHitRate]\"], [\n        enableCacheTracking\n    ]);\n    // Service Worker message handler for cache events\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"usePerformanceOptimization.useEffect\": ()=>{\n            if (!enableCacheTracking || \"object\" === 'undefined') return;\n            const handleSWMessage = {\n                \"usePerformanceOptimization.useEffect.handleSWMessage\": (event)=>{\n                    var _event_data, _event_data1;\n                    if (((_event_data = event.data) === null || _event_data === void 0 ? void 0 : _event_data.type) === 'CACHE_HIT') {\n                        cacheHits.current++;\n                        cacheRequests.current++;\n                        trackCacheHitRate();\n                    } else if (((_event_data1 = event.data) === null || _event_data1 === void 0 ? void 0 : _event_data1.type) === 'CACHE_MISS') {\n                        cacheRequests.current++;\n                        trackCacheHitRate();\n                    }\n                }\n            }[\"usePerformanceOptimization.useEffect.handleSWMessage\"];\n            if ('serviceWorker' in navigator) {\n                navigator.serviceWorker.addEventListener('message', handleSWMessage);\n                return ({\n                    \"usePerformanceOptimization.useEffect\": ()=>{\n                        navigator.serviceWorker.removeEventListener('message', handleSWMessage);\n                    }\n                })[\"usePerformanceOptimization.useEffect\"];\n            }\n        }\n    }[\"usePerformanceOptimization.useEffect\"], [\n        enableCacheTracking,\n        trackCacheHitRate\n    ]);\n    // Navigation timing\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"usePerformanceOptimization.useEffect\": ()=>{\n            if (!enableMonitoring) return;\n            const measureNavigation = {\n                \"usePerformanceOptimization.useEffect.measureNavigation\": ()=>{\n                    const navigation = performance.getEntriesByType('navigation')[0];\n                    if (navigation) {\n                        const navigationTime = navigation.loadEventEnd - navigation.navigationStart;\n                        setMetrics({\n                            \"usePerformanceOptimization.useEffect.measureNavigation\": (prev)=>({\n                                    ...prev,\n                                    navigationTime\n                                })\n                        }[\"usePerformanceOptimization.useEffect.measureNavigation\"]);\n                    }\n                }\n            }[\"usePerformanceOptimization.useEffect.measureNavigation\"];\n            // Measure after page load\n            if (document.readyState === 'complete') {\n                measureNavigation();\n            } else {\n                window.addEventListener('load', measureNavigation);\n                return ({\n                    \"usePerformanceOptimization.useEffect\": ()=>window.removeEventListener('load', measureNavigation)\n                })[\"usePerformanceOptimization.useEffect\"];\n            }\n        }\n    }[\"usePerformanceOptimization.useEffect\"], [\n        enableMonitoring\n    ]);\n    // Periodic monitoring\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"usePerformanceOptimization.useEffect\": ()=>{\n            if (!enableMonitoring) return;\n            const interval = setInterval({\n                \"usePerformanceOptimization.useEffect.interval\": ()=>{\n                    trackMemoryUsage();\n                    analyzeBundleSize();\n                }\n            }[\"usePerformanceOptimization.useEffect.interval\"], 5000); // Check every 5 seconds\n            return ({\n                \"usePerformanceOptimization.useEffect\": ()=>clearInterval(interval)\n            })[\"usePerformanceOptimization.useEffect\"];\n        }\n    }[\"usePerformanceOptimization.useEffect\"], [\n        enableMonitoring,\n        trackMemoryUsage,\n        analyzeBundleSize\n    ]);\n    // Performance optimization suggestions\n    const getOptimizationSuggestions = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"usePerformanceOptimization.useCallback[getOptimizationSuggestions]\": ()=>{\n            const suggestions = [];\n            if (metrics.renderTime > 100) {\n                suggestions.push('Consider memoizing expensive calculations');\n                suggestions.push('Use React.memo for component optimization');\n                suggestions.push('Implement virtualization for large lists');\n            }\n            if (metrics.memoryUsage && metrics.memoryUsage.used > 50 * 1024 * 1024) {\n                suggestions.push('Check for memory leaks');\n                suggestions.push('Optimize image sizes and formats');\n                suggestions.push('Implement proper cleanup in useEffect');\n            }\n            if (metrics.bundleSize && metrics.bundleSize > 1024 * 1024) {\n                suggestions.push('Implement code splitting');\n                suggestions.push('Use dynamic imports for heavy components');\n                suggestions.push('Remove unused dependencies');\n            }\n            if (metrics.cacheHitRate !== undefined && metrics.cacheHitRate < 70) {\n                suggestions.push('Improve caching strategy');\n                suggestions.push('Implement service worker caching');\n                suggestions.push('Use browser cache headers');\n            }\n            return suggestions;\n        }\n    }[\"usePerformanceOptimization.useCallback[getOptimizationSuggestions]\"], [\n        metrics\n    ]);\n    // Export performance data for debugging\n    const exportMetrics = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"usePerformanceOptimization.useCallback[exportMetrics]\": ()=>{\n            const exportData = {\n                component: componentName,\n                timestamp: new Date().toISOString(),\n                metrics,\n                suggestions: getOptimizationSuggestions(),\n                userAgent: navigator.userAgent,\n                url: window.location.href\n            };\n            console.group(\"\\uD83D\\uDCCA Performance Report: \".concat(componentName));\n            console.table(metrics);\n            console.log('Suggestions:', getOptimizationSuggestions());\n            console.groupEnd();\n            return exportData;\n        }\n    }[\"usePerformanceOptimization.useCallback[exportMetrics]\"], [\n        componentName,\n        metrics,\n        getOptimizationSuggestions\n    ]);\n    return {\n        metrics,\n        startMeasurement,\n        endMeasurement,\n        trackMemoryUsage,\n        analyzeBundleSize,\n        trackCacheHitRate,\n        getOptimizationSuggestions,\n        exportMetrics\n    };\n}\n// Hook for monitoring page load performance\nfunction usePageLoadPerformance() {\n    const [loadMetrics, setLoadMetrics] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({});\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"usePageLoadPerformance.useEffect\": ()=>{\n            const measurePageLoad = {\n                \"usePageLoadPerformance.useEffect.measurePageLoad\": ()=>{\n                    const navigation = performance.getEntriesByType('navigation')[0];\n                    const paint = performance.getEntriesByType('paint');\n                    const metrics = {};\n                    if (navigation) {\n                        metrics.domContentLoaded = navigation.domContentLoadedEventEnd - navigation.navigationStart;\n                    }\n                    paint.forEach({\n                        \"usePageLoadPerformance.useEffect.measurePageLoad\": (entry)=>{\n                            if (entry.name === 'first-paint') {\n                                metrics.firstPaint = entry.startTime;\n                            } else if (entry.name === 'first-contentful-paint') {\n                                metrics.firstContentfulPaint = entry.startTime;\n                            }\n                        }\n                    }[\"usePageLoadPerformance.useEffect.measurePageLoad\"]);\n                    // Largest Contentful Paint\n                    if ('PerformanceObserver' in window) {\n                        const observer = new PerformanceObserver({\n                            \"usePageLoadPerformance.useEffect.measurePageLoad\": (list)=>{\n                                const entries = list.getEntries();\n                                const lastEntry = entries[entries.length - 1];\n                                metrics.largestContentfulPaint = lastEntry.startTime;\n                                setLoadMetrics({\n                                    \"usePageLoadPerformance.useEffect.measurePageLoad\": (prev)=>({\n                                            ...prev,\n                                            ...metrics\n                                        })\n                                }[\"usePageLoadPerformance.useEffect.measurePageLoad\"]);\n                            }\n                        }[\"usePageLoadPerformance.useEffect.measurePageLoad\"]);\n                        observer.observe({\n                            entryTypes: [\n                                'largest-contentful-paint'\n                            ]\n                        });\n                    }\n                    setLoadMetrics({\n                        \"usePageLoadPerformance.useEffect.measurePageLoad\": (prev)=>({\n                                ...prev,\n                                ...metrics\n                            })\n                    }[\"usePageLoadPerformance.useEffect.measurePageLoad\"]);\n                }\n            }[\"usePageLoadPerformance.useEffect.measurePageLoad\"];\n            if (document.readyState === 'complete') {\n                measurePageLoad();\n            } else {\n                window.addEventListener('load', measurePageLoad);\n                return ({\n                    \"usePageLoadPerformance.useEffect\": ()=>window.removeEventListener('load', measurePageLoad)\n                })[\"usePageLoadPerformance.useEffect\"];\n            }\n        }\n    }[\"usePageLoadPerformance.useEffect\"], []);\n    return loadMetrics;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/usePerformanceOptimization.ts\n"));

/***/ })

}]);