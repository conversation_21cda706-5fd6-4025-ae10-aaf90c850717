"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/landing/TrustBadges.tsx":
/*!************************************************!*\
  !*** ./src/components/landing/TrustBadges.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TrustBadges)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst useCountUp = function(end) {\n    let duration = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 2000, delay = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 0;\n    _s();\n    const [count, setCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [hasStarted, setHasStarted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useCountUp.useEffect\": ()=>{\n            if (!hasStarted) return;\n            const startTime = Date.now() + delay;\n            const endTime = startTime + duration;\n            const timer = setInterval({\n                \"useCountUp.useEffect.timer\": ()=>{\n                    const now = Date.now();\n                    if (now < startTime) return;\n                    const progress = Math.min((now - startTime) / duration, 1);\n                    const easeOutQuart = 1 - Math.pow(1 - progress, 4);\n                    setCount(Math.floor(easeOutQuart * end));\n                    if (progress === 1) {\n                        clearInterval(timer);\n                    }\n                }\n            }[\"useCountUp.useEffect.timer\"], 16);\n            return ({\n                \"useCountUp.useEffect\": ()=>clearInterval(timer)\n            })[\"useCountUp.useEffect\"];\n        }\n    }[\"useCountUp.useEffect\"], [\n        end,\n        duration,\n        delay,\n        hasStarted\n    ]);\n    return {\n        count,\n        startCounting: ()=>setHasStarted(true)\n    };\n};\n_s(useCountUp, \"TCnTLIIeaoJq9uh8nHvQ9hiGmWQ=\");\nfunction TrustBadges() {\n    _s1();\n    const developersCount = useCountUp(15000, 2000, 200);\n    const requestsCount = useCountUp(100, 2000, 400); // 100M\n    const uptimeCount = useCountUp(99.9, 2000, 600);\n    const modelsCount = useCountUp(300, 2000, 800);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative py-20 bg-gradient-to-r from-[#ff6b35] via-[#f7931e] to-[#ff6b35] overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-10\",\n                style: {\n                    backgroundImage: \"\\n            linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),\\n            linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px)\\n          \",\n                    backgroundSize: '50px 50px'\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TrustBadges.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        onViewportEnter: ()=>{\n                            developersCount.startCounting();\n                            requestsCount.startCounting();\n                            uptimeCount.startCounting();\n                            modelsCount.startCounting();\n                        },\n                        className: \"text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-white/90 text-2xl font-bold mb-3\",\n                                children: \"Powering AI Innovation Worldwide\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TrustBadges.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white/70 text-lg\",\n                                children: \"Join thousands of developers enjoying unlimited AI access with RouKey\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TrustBadges.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TrustBadges.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        transition: {\n                            delay: 0.3,\n                            duration: 0.6\n                        },\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-8 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                className: \"group\",\n                                whileHover: {\n                                    scale: 1.05\n                                },\n                                transition: {\n                                    type: \"spring\",\n                                    stiffness: 300\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-4xl md:text-5xl font-bold text-white mb-3 group-hover:text-white/90 transition-colors duration-300\",\n                                        children: [\n                                            developersCount.count.toLocaleString(),\n                                            \"+\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TrustBadges.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-white/80 text-base font-medium\",\n                                        children: \"Active Developers\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TrustBadges.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TrustBadges.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                className: \"group\",\n                                whileHover: {\n                                    scale: 1.05\n                                },\n                                transition: {\n                                    type: \"spring\",\n                                    stiffness: 300\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-4xl md:text-5xl font-bold text-white mb-3 group-hover:text-white/90 transition-colors duration-300\",\n                                        children: [\n                                            requestsCount.count,\n                                            \"M+\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TrustBadges.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-white/80 text-base font-medium\",\n                                        children: \"API Requests\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TrustBadges.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TrustBadges.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                className: \"group\",\n                                whileHover: {\n                                    scale: 1.05\n                                },\n                                transition: {\n                                    type: \"spring\",\n                                    stiffness: 300\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-4xl md:text-5xl font-bold text-white mb-3 group-hover:text-white/90 transition-colors duration-300\",\n                                        children: [\n                                            uptimeCount.count.toFixed(1),\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TrustBadges.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-white/80 text-base font-medium\",\n                                        children: \"Uptime SLA\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TrustBadges.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TrustBadges.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                className: \"group\",\n                                whileHover: {\n                                    scale: 1.05\n                                },\n                                transition: {\n                                    type: \"spring\",\n                                    stiffness: 300\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-4xl md:text-5xl font-bold text-white mb-3 group-hover:text-white/90 transition-colors duration-300\",\n                                        children: [\n                                            modelsCount.count,\n                                            \"+\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TrustBadges.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-white/80 text-base font-medium\",\n                                        children: \"AI Models\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TrustBadges.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TrustBadges.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TrustBadges.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TrustBadges.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TrustBadges.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, this);\n}\n_s1(TrustBadges, \"d8UIxnJDsZMUrXyj7Z/BuPFotBw=\", false, function() {\n    return [\n        useCountUp,\n        useCountUp,\n        useCountUp,\n        useCountUp\n    ];\n});\n_c = TrustBadges;\nvar _c;\n$RefreshReg$(_c, \"TrustBadges\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/TrustBadges.tsx\n"));

/***/ })

});