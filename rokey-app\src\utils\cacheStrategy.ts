'use client';

import { globalCache } from './advancedCache';

// Cache configuration for different data types
export const CACHE_CONFIG = {
  // Static content - long cache duration
  static: {
    ttl: 24 * 60 * 60 * 1000, // 24 hours
    priority: 'high' as const,
    tags: ['static']
  },
  
  // API responses - medium cache duration
  api: {
    ttl: 5 * 60 * 1000, // 5 minutes
    priority: 'medium' as const,
    tags: ['api']
  },
  
  // User data - short cache duration
  user: {
    ttl: 2 * 60 * 1000, // 2 minutes
    priority: 'high' as const,
    tags: ['user']
  },
  
  // System status - very short cache
  system: {
    ttl: 30 * 1000, // 30 seconds
    priority: 'low' as const,
    tags: ['system']
  },
  
  // Pricing data - long cache (rarely changes)
  pricing: {
    ttl: 60 * 60 * 1000, // 1 hour
    priority: 'medium' as const,
    tags: ['pricing']
  }
};

// Cache keys for consistent naming
export const CACHE_KEYS = {
  // Landing page data
  LANDING_FEATURES: 'landing:features',
  LANDING_TESTIMONIALS: 'landing:testimonials',
  LANDING_TRUST_BADGES: 'landing:trust-badges',
  
  // Pricing data
  PRICING_TIERS: 'pricing:tiers',
  PRICING_COMPARISON: 'pricing:comparison',
  
  // System data
  SYSTEM_STATUS: 'system:status',
  SYSTEM_MODELS: 'system:models',
  
  // User data
  USER_CONFIGS: 'user:configs',
  USER_ANALYTICS: 'user:analytics',
  USER_ACTIVITY: 'user:activity',
  
  // Auth data
  AUTH_SESSION: 'auth:session',
  AUTH_PERMISSIONS: 'auth:permissions'
};

// Preload critical data for landing page
export async function preloadLandingPageData() {
  const criticalData = [
    // Preload pricing data
    {
      key: CACHE_KEYS.PRICING_TIERS,
      fetcher: () => fetch('/api/pricing/tiers').then(r => r.json()),
      config: CACHE_CONFIG.pricing
    },
    
    // Preload system status
    {
      key: CACHE_KEYS.SYSTEM_STATUS,
      fetcher: () => fetch('/api/system-status').then(r => r.json()),
      config: CACHE_CONFIG.system
    }
  ];

  const promises = criticalData.map(async ({ key, fetcher, config }) => {
    try {
      // Check if already cached
      const cached = globalCache.get(key);
      if (cached) return cached;
      
      // Fetch and cache
      const data = await fetcher();
      globalCache.set(key, data, config);
      return data;
    } catch (error) {
      console.warn(`Failed to preload ${key}:`, error);
      return null;
    }
  });

  await Promise.allSettled(promises);
}

// Intelligent prefetching based on user behavior
export class IntelligentPrefetcher {
  private static instance: IntelligentPrefetcher;
  private prefetchQueue: Set<string> = new Set();
  private isProcessing = false;
  private userBehavior: Map<string, number> = new Map();

  static getInstance(): IntelligentPrefetcher {
    if (!IntelligentPrefetcher.instance) {
      IntelligentPrefetcher.instance = new IntelligentPrefetcher();
    }
    return IntelligentPrefetcher.instance;
  }

  // Track user navigation patterns
  trackNavigation(from: string, to: string) {
    const pattern = `${from}->${to}`;
    const count = this.userBehavior.get(pattern) || 0;
    this.userBehavior.set(pattern, count + 1);
    
    // If pattern is common, prefetch related data
    if (count > 2) {
      this.schedulePrefetch(to);
    }
  }

  // Schedule prefetch for a route
  schedulePrefetch(route: string) {
    if (this.prefetchQueue.has(route)) return;
    
    this.prefetchQueue.add(route);
    this.processPrefetchQueue();
  }

  // Process prefetch queue
  private async processPrefetchQueue() {
    if (this.isProcessing || this.prefetchQueue.size === 0) return;
    
    this.isProcessing = true;
    
    for (const route of this.prefetchQueue) {
      try {
        await this.prefetchRoute(route);
        this.prefetchQueue.delete(route);
      } catch (error) {
        console.warn(`Failed to prefetch ${route}:`, error);
      }
      
      // Small delay to avoid overwhelming the browser
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    this.isProcessing = false;
  }

  // Prefetch data for a specific route
  private async prefetchRoute(route: string) {
    const prefetchMap: Record<string, () => Promise<void>> = {
      '/dashboard': () => this.prefetchDashboardData(),
      '/pricing': () => this.prefetchPricingData(),
      '/auth/signup': () => this.prefetchAuthData(),
      '/features': () => this.prefetchFeaturesData()
    };

    const prefetcher = prefetchMap[route];
    if (prefetcher) {
      await prefetcher();
    }
  }

  // Prefetch dashboard data
  private async prefetchDashboardData() {
    const promises = [
      this.cacheIfNotExists(CACHE_KEYS.USER_CONFIGS, '/api/custom-configs', CACHE_CONFIG.user),
      this.cacheIfNotExists(CACHE_KEYS.USER_ANALYTICS, '/api/analytics', CACHE_CONFIG.user),
      this.cacheIfNotExists(CACHE_KEYS.SYSTEM_STATUS, '/api/system-status', CACHE_CONFIG.system)
    ];
    
    await Promise.allSettled(promises);
  }

  // Prefetch pricing data
  private async prefetchPricingData() {
    const promises = [
      this.cacheIfNotExists(CACHE_KEYS.PRICING_TIERS, '/api/pricing/tiers', CACHE_CONFIG.pricing),
      this.cacheIfNotExists(CACHE_KEYS.PRICING_COMPARISON, '/api/pricing/comparison', CACHE_CONFIG.pricing)
    ];
    
    await Promise.allSettled(promises);
  }

  // Prefetch auth data
  private async prefetchAuthData() {
    // Prefetch auth-related static data
    const promises = [
      this.cacheIfNotExists(CACHE_KEYS.PRICING_TIERS, '/api/pricing/tiers', CACHE_CONFIG.pricing)
    ];
    
    await Promise.allSettled(promises);
  }

  // Prefetch features data
  private async prefetchFeaturesData() {
    const promises = [
      this.cacheIfNotExists(CACHE_KEYS.LANDING_FEATURES, '/api/features', CACHE_CONFIG.static),
      this.cacheIfNotExists(CACHE_KEYS.SYSTEM_MODELS, '/api/models', CACHE_CONFIG.static)
    ];
    
    await Promise.allSettled(promises);
  }

  // Helper to cache data if not already cached
  private async cacheIfNotExists(key: string, url: string, config: any) {
    const cached = globalCache.get(key);
    if (cached) return cached;
    
    try {
      const response = await fetch(url);
      if (response.ok) {
        const data = await response.json();
        globalCache.set(key, data, config);
        return data;
      }
    } catch (error) {
      console.warn(`Failed to cache ${key}:`, error);
    }
  }
}

// Cache invalidation strategies
export class CacheInvalidator {
  // Invalidate cache by tags
  static invalidateByTags(tags: string[]) {
    tags.forEach(tag => {
      globalCache.invalidateByTag(tag);
    });
  }

  // Invalidate user-specific cache
  static invalidateUserCache() {
    this.invalidateByTags(['user', 'auth']);
  }

  // Invalidate system cache
  static invalidateSystemCache() {
    this.invalidateByTags(['system']);
  }

  // Smart invalidation based on data changes
  static smartInvalidate(dataType: string, operation: 'create' | 'update' | 'delete') {
    const invalidationMap: Record<string, string[]> = {
      'user-config': ['user', 'api'],
      'user-profile': ['user', 'auth'],
      'system-status': ['system'],
      'pricing': ['pricing', 'static']
    };

    const tagsToInvalidate = invalidationMap[dataType];
    if (tagsToInvalidate) {
      this.invalidateByTags(tagsToInvalidate);
    }
  }
}

// Initialize prefetcher
export const prefetcher = IntelligentPrefetcher.getInstance();

// Export cache utilities
export {
  globalCache as cache,
  CacheInvalidator as invalidator
};
